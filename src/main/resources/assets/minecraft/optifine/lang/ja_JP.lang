# Contributors of Japanese localization #
#   takanasayo 2012-12-01 ---- 2013-07-15
#   CrafterKina 2014-01-04 ---- 
#   syokkendesuyo 2020-08-04

# General
of.general.ambiguous=不明確
of.general.compact=コンパクト
of.general.custom=カスタム
of.general.from=由来
of.general.id=ID
of.general.max=最大限
of.general.restart=再起動
of.general.smart=スマート

# Keys
of.key.zoom=ズーム

# Message
of.message.aa.shaders1=アンチエイリアスはシェーダーと共存できません。
of.message.aa.shaders2=このオプションを有効にするには、シェーダーを無効にしてください。

of.message.af.shaders1=異方性フィルタリングはシェーダーと共存できません。
of.message.af.shaders2=このオプションを有効にするには、シェーダーを無効にしてください。

of.message.fr.shaders1=描画の最適化はシェーダーと共存できません。
of.message.fr.shaders2=このオプションを有効にするには、シェーダーを無効にしてください。

of.message.an.shaders1=3Dアナグリフはシェーダーと共存できません。
of.message.an.shaders2=このオプションを有効にするには、シェーダーを無効にしてください。

of.message.shaders.aa1=シェーダーはアンチエイリアスと共存できません。
of.message.shaders.aa2=品質の設定 → アンチエイリアス をオフにし、ゲームを再起動してください。

of.message.shaders.af1=シェーダーは異方性フィルタリングと共存できません。
of.message.shaders.af2=品質の設定 → 異方性フィルタリング をオフにしてください。

of.message.shaders.fr1=シェーダーは描画の最適化と共存できません。
of.message.shaders.fr2=演出の設定 → 描画の最適化 をオフにしてください。

of.message.shaders.gf1=シェーダーはグラフィックスの「最高設定！」と互換性がありません。
of.message.shaders.gf2=グラフィックスの設定を描画優先または処理優先に変更してください。

of.message.shaders.an1=シェーダーは3Dアナグリフと共存できません。
of.message.shaders.an2=その他の設定 → 3Dアナグリフ をオフにしてください。

of.message.shaders.nv1=このシェーダーパックはより新しいOptiFine (%s) を要求します。
of.message.shaders.nv2=続行しますか？

of.message.newVersion=新しい§eOptiFine§fが公開されました: §e%s§f
of.message.java64Bit=最良のパフォーマンスを発揮するためには§e64-bit Java§f使用してください。
of.message.openglError=§eOpenGLエラー§f: %s (%s)

of.message.shaders.loading=シェーダーをロード中: %s

of.message.other.reset=全ての設定を元の状態に戻してもよろしいですか？

of.message.loadingVisibleChunks=可視チャンクを読み込み中

# Skin customization

of.options.skinCustomisation.ofCape=OptiFineマント...

of.options.capeOF.title=OptiFineマント
of.options.capeOF.openEditor=OptiFineマントエディター
of.options.capeOF.reloadCape=マントを再読み込み
of.options.capeOF.copyEditorLink=リンクをクリップボードにコピー

of.message.capeOF.openEditor=OptiFineマントエディターは、ウェブブラウザで開く必要があります。
of.message.capeOF.openEditorError=ウェブブラウザでエディタリンクを開く時にエラーが発生しました。
of.message.capeOF.reloadCape=マントは15秒で再読込されます。

of.message.capeOF.error1=Mojang認証に失敗しました。
of.message.capeOF.error2=エラー: %s

# Video settings

options.graphics.tooltip.1=グラフィックス
options.graphics.tooltip.2=  処理優先 - 低品質、低負荷
options.graphics.tooltip.3=  描画優先 - 高品質、高負荷
options.graphics.tooltip.4=  最高設定！ - 透過ブロックに対する最高品質、非常に高負荷
options.graphics.tooltip.5=葉の透過、アイテムやMobの影、ドロップアイテムの3D描写、厚みのある雲、
options.graphics.tooltip.6=水の2パスレンダリング、といったグラフィック効果を変更します。
options.graphics.tooltip.7=最高品質！はシェーダーと互換性がありません。                                                                            

of.options.renderDistance.tiny=最短
of.options.renderDistance.short=短い
of.options.renderDistance.normal=普通
of.options.renderDistance.far=遠い
of.options.renderDistance.extreme=過激
of.options.renderDistance.insane=狂気
of.options.renderDistance.ludicrous=不条理

options.renderDistance.tooltip.1=描画距離
options.renderDistance.tooltip.2=  2 最短 - 32m (最低負荷)
options.renderDistance.tooltip.3=  8 普通 - 128m (普通)
options.renderDistance.tooltip.4=  16 遠い - 256m (高負荷)
options.renderDistance.tooltip.5=  32 過激 - 512m (超高負荷！) 大量のリソースを要求します
options.renderDistance.tooltip.6=  48 狂気 - 768m、2GB以上のメモリー割り当てが必要です
options.renderDistance.tooltip.7=  64 不条理 - 1024m、3GB以上のメモリー割り当てが必要です
options.renderDistance.tooltip.8=遠いを超える描画距離はシングルのワールドでのみ効果があります。

options.entityDistanceScaling.tooltip.1=エンティティの描画距離
options.entityDistanceScaling.tooltip.2=  50%% - 低負荷
options.entityDistanceScaling.tooltip.3=  100%% - デフォルト
options.entityDistanceScaling.tooltip.4=  500%% - 高負荷
options.entityDistanceScaling.tooltip.5=エンティティが表示される最大距離を変更します。

options.ao.tooltip.1=スムースライティング
options.ao.tooltip.2=  オフ - スムースライティングを使用しない (低負荷)
options.ao.tooltip.3=  最小 - 単純なスムースライティング (高負荷)
options.ao.tooltip.4=  最大 - 複雑なスムースライティング (最高負荷)

options.framerateLimit.tooltip.1=最大フレームレート
options.framerateLimit.tooltip.2=  垂直同期 - モニターのフレームレートに合わせる (60、30、20)
options.framerateLimit.tooltip.3=  5〜255 - フレームレートを設定した値までに制限する
options.framerateLimit.tooltip.4=  無制限 - フレームレートを制限しない (最低負荷)
options.framerateLimit.tooltip.5=たとえ制限に達していなくとも、
options.framerateLimit.tooltip.6=フレームレートを抑制します。
of.options.framerateLimit.vsync=垂直同期

of.options.AO_LEVEL=スムースライティングの程度
of.options.AO_LEVEL.tooltip.1=スムースライティングの程度
of.options.AO_LEVEL.tooltip.2=  オフ - 影なし
of.options.AO_LEVEL.tooltip.3=  50%% - 明るい影
of.options.AO_LEVEL.tooltip.4=  100%% - 暗い影

options.viewBobbing.tooltip.1=画面の揺れ
options.viewBobbing.tooltip.2=ミップマップを使う場合には、最良の結果のためにオフにしてください。

options.guiScale.tooltip.1=GUIの大きさ
options.guiScale.tooltip.2=  自動 - 最大サイズ
options.guiScale.tooltip.3=  小さい、普通、大きい - 1倍から3倍
options.guiScale.tooltip.4=  4x から 10x - 4k画面で有効
options.guiScale.tooltip.5=奇数倍 (1x、3x、5x ...) はUnicodeフォントと互換がありません。
options.guiScale.tooltip.6=GUIは小さいほうが処理が速くなるかもしれません。

options.vbo=VBO の使用
options.vbo.tooltip.1=頂点バッファオブジェクト
options.vbo.tooltip.2=描画方式を置き換え、通常より
options.vbo.tooltip.3=5〜10%%処理を軽くできます。

options.gamma.tooltip.1=暗い物体の明るさの変更
options.gamma.tooltip.2=  暗い - 通常の明るさ
options.gamma.tooltip.3=  1〜99%% - 明るさを調整する
options.gamma.tooltip.4=  明るい - 最大の明るさ
options.gamma.tooltip.5=完全に黒い物体の明るさは、
options.gamma.tooltip.6=変更できません。

options.anaglyph.tooltip.1=3Dアナグリフ
options.anaglyph.tooltip.2=それぞれの目に別々の色を使うことで
options.anaglyph.tooltip.3=立体視を可能にする機能を有効にします。
options.anaglyph.tooltip.4=赤青3Dメガネが必要です。

options.attackIndicator.tooltip.1=攻撃インジケータの位置を設定します。
options.attackIndicator.tooltip.2=  クロスヘア - クロスヘアの下
options.attackIndicator.tooltip.3=  ホットバー - ホットバーの横
options.attackIndicator.tooltip.4=  オフ - 攻撃インジケータ無し
options.attackIndicator.tooltip.5=攻撃インジケータは、現在装備しているアイテムの
options.attackIndicator.tooltip.6=攻撃力を表示します。

of.options.ALTERNATE_BLOCKS=代替ブロック
of.options.ALTERNATE_BLOCKS.tooltip.1=代替ブロック
of.options.ALTERNATE_BLOCKS.tooltip.2=幾つかのブロックで代替モデルを使用します。
of.options.ALTERNATE_BLOCKS.tooltip.3=選択されたリソースパックに依存します。

of.options.FOG_FANCY=霧の種類
of.options.FOG_FANCY.tooltip.1=霧の種類の設定
of.options.FOG_FANCY.tooltip.2=  処理優先 - 低負荷な霧
of.options.FOG_FANCY.tooltip.3=  描画優先 - 高負荷な霧 より良く見える
of.options.FOG_FANCY.tooltip.4=  オフ - 霧なし 最低負荷
of.options.FOG_FANCY.tooltip.5=グラフィックカードが対応している場合のみ
of.options.FOG_FANCY.tooltip.6=美麗な霧を使用できます。

of.options.FOG_START=霧の距離
of.options.FOG_START.tooltip.1=プレイヤーと霧の距離の設定
of.options.FOG_START.tooltip.2=  0.2 - プレイヤーと霧の距離を最も近くする
of.options.FOG_START.tooltip.3=  0.8 - プレイヤーと霧の距離を最も遠くする
of.options.FOG_START.tooltip.4=この設定は概ねパフォーマンスに影響を与えません。

of.options.CHUNK_LOADING=チャンク読み込み
of.options.CHUNK_LOADING.tooltip.1=チャンク読み込み
of.options.CHUNK_LOADING.tooltip.2=  デフォルト - チャンク読み込み時、FPSは安定しない。
of.options.CHUNK_LOADING.tooltip.3=  滑らか - フレームレート低下を抑制。
of.options.CHUNK_LOADING.tooltip.4=  マルチコア - フレームレートのさらなる安定化、ワールドの読み込み処理が三倍ほど速くなる。
of.options.CHUNK_LOADING.tooltip.5=滑らかやマルチコアであれば、チャンク読み込みに起因する
of.options.CHUNK_LOADING.tooltip.6=カクつきとフリーズを除けます。
of.options.CHUNK_LOADING.tooltip.7=マルチコアでは二番目のCPUを用いることでワールドの読み込み処理が
of.options.CHUNK_LOADING.tooltip.8=三倍ほど速くでき、FPSも改善できます。
of.options.chunkLoading.smooth=滑らか
of.options.chunkLoading.multiCore=マルチコア

of.options.shaders=シェーダーの詳細設定...
of.options.shadersTitle=シェーダーの詳細設定

of.options.shaders.packNone=なし
of.options.shaders.packDefault=(内部シェーダー)

of.options.shaders.ANTIALIASING=アンチエイリアス
of.options.shaders.ANTIALIASING.tooltip.1=アンチエイリアス
of.options.shaders.ANTIALIASING.tooltip.2=  オフ -  (初期値) アンチエイリアスを行わない (低負荷)
of.options.shaders.ANTIALIASING.tooltip.3=  FXAA 2x、4x - アンチエイリアスされた輪郭 (高負荷)
of.options.shaders.ANTIALIASING.tooltip.4=FXAAは輪郭のギザギザや急激な色の変化を
of.options.shaders.ANTIALIASING.tooltip.5=滑らかにする後処理効果です。
of.options.shaders.ANTIALIASING.tooltip.6=この機能は旧来のアンチエイリアス処理より低負荷で
of.options.shaders.ANTIALIASING.tooltip.7=シェーダーや描画の最適化機能と共存できます。

of.options.shaders.NORMAL_MAP=法線マップ
of.options.shaders.NORMAL_MAP.tooltip.1=法線マップ
of.options.shaders.NORMAL_MAP.tooltip.2=  オン -  (初期値) 法線マップを有効化
of.options.shaders.NORMAL_MAP.tooltip.3=  オフ - 法線マップを無効化
of.options.shaders.NORMAL_MAP.tooltip.4=法線マップはシェーダーパックが平坦なモデルの表面における
of.options.shaders.NORMAL_MAP.tooltip.5=立体的な形状をシミュレーションするために用いられます。
of.options.shaders.NORMAL_MAP.tooltip.6=法線マップテクスチャは現在使用中の
of.options.shaders.NORMAL_MAP.tooltip.7=リソースパックに依存します。

of.options.shaders.SPECULAR_MAP=鏡面反射マップ
of.options.shaders.SPECULAR_MAP.tooltip.1=鏡面反射マップ
of.options.shaders.SPECULAR_MAP.tooltip.2=  オン -  (初期値) 鏡面反射マップを有効化
of.options.shaders.SPECULAR_MAP.tooltip.3=  オフ - 鏡面反射マップを無効化
of.options.shaders.SPECULAR_MAP.tooltip.4=鏡面反射マップはシェーダーパックが特殊な反射効果を
of.options.shaders.SPECULAR_MAP.tooltip.5=シミュレーションするために用いられます。
of.options.shaders.SPECULAR_MAP.tooltip.6=法線マップテクスチャは現在使用中の
of.options.shaders.SPECULAR_MAP.tooltip.7=リソースパックに依存します。

of.options.shaders.RENDER_RES_MUL=描画の品質
of.options.shaders.RENDER_RES_MUL.tooltip.1=描画の品質
of.options.shaders.RENDER_RES_MUL.tooltip.2=  0.5x - 低い (最低負荷)
of.options.shaders.RENDER_RES_MUL.tooltip.3=  1x - 標準  (初期値)
of.options.shaders.RENDER_RES_MUL.tooltip.4=  2x - 高い (最高負荷)
of.options.shaders.RENDER_RES_MUL.tooltip.5=描画の品質設定ではシェーダーパックに渡す
of.options.shaders.RENDER_RES_MUL.tooltip.6=テクスチャのサイズを制御します。
of.options.shaders.RENDER_RES_MUL.tooltip.7=低い値は4Kディスプレイに有用でしょう。
of.options.shaders.RENDER_RES_MUL.tooltip.8=高い値はアンチエイリアスをかけたかのように働きます。

of.options.shaders.SHADOW_RES_MUL=陰影の品質
of.options.shaders.SHADOW_RES_MUL.tooltip.1=陰影の品質
of.options.shaders.SHADOW_RES_MUL.tooltip.2=  0.5x - 低い (最低負荷)
of.options.shaders.SHADOW_RES_MUL.tooltip.3=  1x - 標準  (初期値)
of.options.shaders.SHADOW_RES_MUL.tooltip.4=  2x - 高い (最高負荷)
of.options.shaders.SHADOW_RES_MUL.tooltip.5=陰影の品質設定ではシェーダーパックが用いる
of.options.shaders.SHADOW_RES_MUL.tooltip.6=シャドウマップテクスチャのサイズを制御します。
of.options.shaders.SHADOW_RES_MUL.tooltip.7=低い値 = 厳格でない、荒い影。
of.options.shaders.SHADOW_RES_MUL.tooltip.8=高い値 = 細密で、繊細な影。

of.options.shaders.HAND_DEPTH_MUL=手の遠近
of.options.shaders.HAND_DEPTH_MUL.tooltip.1=手の遠近
of.options.shaders.HAND_DEPTH_MUL.tooltip.2=  0.5x - カメラに近い手
of.options.shaders.HAND_DEPTH_MUL.tooltip.3=  1x -  (初期値)
of.options.shaders.HAND_DEPTH_MUL.tooltip.4=  2x - カメラから遠い手
of.options.shaders.HAND_DEPTH_MUL.tooltip.5=手の遠近設定では手に持ったものがカメラから
of.options.shaders.HAND_DEPTH_MUL.tooltip.6=どれだけ遠いかを制御します。
of.options.shaders.HAND_DEPTH_MUL.tooltip.7=被写界深度効果を持つシェーダーパックによる
of.options.shaders.HAND_DEPTH_MUL.tooltip.8=手に持ったもののブラーも変更します。

of.options.shaders.CLOUD_SHADOW=雲の影

of.options.shaders.OLD_HAND_LIGHT=古い手持ち光源
of.options.shaders.OLD_HAND_LIGHT.tooltip.1=古い手持ち光源
of.options.shaders.OLD_HAND_LIGHT.tooltip.2=  デフォルト - シェーダーパックによる制御
of.options.shaders.OLD_HAND_LIGHT.tooltip.3=  オン - 古い手持ち光源システムを用いる
of.options.shaders.OLD_HAND_LIGHT.tooltip.4=  オフ - 新たな手持ち光源システムを用いる
of.options.shaders.OLD_HAND_LIGHT.tooltip.5=古い手持ち光源システムはメインハンドの
of.options.shaders.OLD_HAND_LIGHT.tooltip.6=光源アイテムしか認識できないシェーダーパックを
of.options.shaders.OLD_HAND_LIGHT.tooltip.7=オフハンドも認識できるようにします。

of.options.shaders.OLD_LIGHTING=古い照明効果
of.options.shaders.OLD_LIGHTING.tooltip.1=古い照明効果
of.options.shaders.OLD_LIGHTING.tooltip.2=  デフォルト - シェーダーパックによる制御
of.options.shaders.OLD_LIGHTING.tooltip.3=  オン - 古い照明効果を用いる
of.options.shaders.OLD_LIGHTING.tooltip.4=  オフ - 古い照明効果を用いない
of.options.shaders.OLD_LIGHTING.tooltip.5=古い照明効果設定ではバニラがブロックの側面に
of.options.shaders.OLD_LIGHTING.tooltip.6=適用した明かりの描画を制御します。 
of.options.shaders.OLD_LIGHTING.tooltip.7=影を扱うシェーダーパックは多くの場合
of.options.shaders.OLD_LIGHTING.tooltip.8=太陽の位置に基づくはるかに良い影を提供します。

of.options.shaders.DOWNLOAD=シェーダーのダウンロード
of.options.shaders.DOWNLOAD.tooltip.1=シェーダーのダウンロード
of.options.shaders.DOWNLOAD.tooltip.2= 
of.options.shaders.DOWNLOAD.tooltip.3=シェーダーのダウンロードページをブラウザで開きます。
of.options.shaders.DOWNLOAD.tooltip.4=ダウンロードしたシェーダーパックは「シェーダーフォルダー」に入れると
of.options.shaders.DOWNLOAD.tooltip.5=導入されたシェーダーの一覧に表示されます。

of.options.shaders.SHADER_PACK=シェーダーパック

of.options.shaders.shadersFolder=シェーダーフォルダー
of.options.shaders.shaderOptions=シェーダーのオプション設定...

of.options.shaderOptionsTitle=シェーダーのオプション設定

of.options.quality=品質の設定...
of.options.qualityTitle=品質

of.options.details=細部の設定...
of.options.detailsTitle=細部

of.options.performance=パフォーマンスの設定...
of.options.performanceTitle=パフォーマンス

of.options.animations=アニメーションの設定...
of.options.animationsTitle=アニメーション

of.options.other=その他の設定...
of.options.otherTitle=その他

of.options.other.reset=ビデオ設定を初期化する...

of.shaders.profile=プロファイル

# Quality

of.options.mipmap.bilinear=バイリニア補間
of.options.mipmap.linear=線形補間
of.options.mipmap.nearest=ニアレストネイバー補間
of.options.mipmap.trilinear=トライリニア補間

options.mipmapLevels.tooltip.1=遠くの物体における視覚効果を
options.mipmapLevels.tooltip.2=テクスチャのスムージングによって改善します。
options.mipmapLevels.tooltip.3=  オフ - スムージングを行わない
options.mipmapLevels.tooltip.4=  1 - 最小限のスムージング
options.mipmapLevels.tooltip.5=  最大限 - 最大限のスムージング
options.mipmapLevels.tooltip.6=この設定は概ねパフォーマンスに影響を与えません。

of.options.MIPMAP_TYPE=ミップマップの種類
of.options.MIPMAP_TYPE.tooltip.1=遠くの物体における視覚効果を
of.options.MIPMAP_TYPE.tooltip.2=テクスチャのスムージングによって改善します。
of.options.MIPMAP_TYPE.tooltip.3=  ニアレストネイバー補間 - おおまかなスムージング (低負荷)
of.options.MIPMAP_TYPE.tooltip.4=  線形補間 - 通常のスムージング
of.options.MIPMAP_TYPE.tooltip.5=  バイリニア補間 - 繊細なスムージング
of.options.MIPMAP_TYPE.tooltip.6=  トライリニア補間 - 最高級のスムージング (高負荷)


of.options.AA_LEVEL=アンチエイリアス
of.options.AA_LEVEL.tooltip.1=アンチエイリアス
of.options.AA_LEVEL.tooltip.2= オフ -  (初期値) アンチエイリアスを行わない (低負荷)
of.options.AA_LEVEL.tooltip.3= 2〜16 - アンチエイリアスされた輪郭 (高負荷)
of.options.AA_LEVEL.tooltip.4=アンチエイリアスは輪郭のギザギザや
of.options.AA_LEVEL.tooltip.5=急激な色の変化を滑らかに表現します。
of.options.AA_LEVEL.tooltip.6=有効化すると大幅にFPSが低下します。
of.options.AA_LEVEL.tooltip.7=グラフィックカードの仕様によって一部のレベルまたは全レベルが使用できない場合もあります。
of.options.AA_LEVEL.tooltip.8=この設定はMinecraftの再起動が必要です！

of.options.AF_LEVEL=異方性フィルタリング
of.options.AF_LEVEL.tooltip.1=異方性フィルタリング
of.options.AF_LEVEL.tooltip.2= オフ -  (初期値) 標準的なテクスチャの細部 (低負荷)
of.options.AF_LEVEL.tooltip.3= 2〜16 - ミップマップされたテクスチャでのより繊細な細部 (高負荷)
of.options.AF_LEVEL.tooltip.4=異方性フィルタリングはミップマップされたテクスチャの
of.options.AF_LEVEL.tooltip.5=細部を復元します。
of.options.AF_LEVEL.tooltip.6=有効化すると大幅にFPSが下がります。

of.options.CLEAR_WATER=水の透過
of.options.CLEAR_WATER.tooltip.1=水の透過
of.options.CLEAR_WATER.tooltip.2=  オン - 澄んだ透明な水
of.options.CLEAR_WATER.tooltip.3=  オフ - 普通の水

of.options.RANDOM_ENTITIES=ランダムエンティティ
of.options.RANDOM_ENTITIES.tooltip.1=ランダムエンティティ
of.options.RANDOM_ENTITIES.tooltip.2=  オフ - ランダムなテクスチャをエンティティに用いない 低負荷
of.options.RANDOM_ENTITIES.tooltip.3=  オン - ランダムなテクスチャをエンティティに用いる 高負荷
of.options.RANDOM_ENTITIES.tooltip.4=ランダムエンティティはランダムなテクスチャをエンティティに適用します。
of.options.RANDOM_ENTITIES.tooltip.5=複数のエンティティテクスチャを含むリソースパックが必要です。

of.options.BETTER_GRASS=より良い芝生
of.options.BETTER_GRASS.tooltip.1=より良い芝生
of.options.BETTER_GRASS.tooltip.2=  オフ - 通常の側面のテクスチャを用いる 低負荷
of.options.BETTER_GRASS.tooltip.3=  処理優先 - 側面も草で覆う 高負荷
of.options.BETTER_GRASS.tooltip.4=  描画優先 - 草ブロックと繋がるように側面を草で覆う 最高負荷

of.options.BETTER_SNOW=より良い雪
of.options.BETTER_SNOW.tooltip.1=より良い雪
of.options.BETTER_SNOW.tooltip.2=  オフ - 通常の雪 低負荷
of.options.BETTER_SNOW.tooltip.3=  オン - より良い雪 高負荷
of.options.BETTER_SNOW.tooltip.4=隣接したブロックに雪が積もっているものがあった時に
of.options.BETTER_SNOW.tooltip.5=透過性のあるブロック (フェンス、背の高い草) の下でも雪を描画します。

of.options.CUSTOM_FONTS=カスタムフォント
of.options.CUSTOM_FONTS.tooltip.1=カスタムフォント
of.options.CUSTOM_FONTS.tooltip.2=  オン - カスタムフォントを使う (初期値) 高負荷
of.options.CUSTOM_FONTS.tooltip.3=  オフ - デフォルトのフォントを使う 低負荷
of.options.CUSTOM_FONTS.tooltip.4=カスタムフォントは現在使用中の
of.options.CUSTOM_FONTS.tooltip.5=リソースパックに依存します。

of.options.CUSTOM_COLORS=カスタムカラー
of.options.CUSTOM_COLORS.tooltip.1=カスタムカラー
of.options.CUSTOM_COLORS.tooltip.2=  オン - カスタムカラーを使う (初期値) 高負荷
of.options.CUSTOM_COLORS.tooltip.3=  オフ - デフォルトの色を使う 低負荷
of.options.CUSTOM_COLORS.tooltip.4=カスタムカラーは現在使用中の
of.options.CUSTOM_COLORS.tooltip.5=リソースパックに依存します。

of.options.SWAMP_COLORS=湿地帯色
of.options.SWAMP_COLORS.tooltip.1=湿地帯色
of.options.SWAMP_COLORS.tooltip.2=  オン - 湿地帯色を使う 高負荷
of.options.SWAMP_COLORS.tooltip.3=  オフ - 湿地帯色を使わない 低負荷
of.options.SWAMP_COLORS.tooltip.4=湿地帯色は草、葉、蔦、水に影響を与えます。

of.options.SMOOTH_BIOMES=滑らかなバイオーム
of.options.SMOOTH_BIOMES.tooltip.1=滑らかなバイオーム
of.options.SMOOTH_BIOMES.tooltip.2=  オン - バイオームの境界を滑らかにする (初期値) 高負荷
of.options.SMOOTH_BIOMES.tooltip.3=  オフ - バイオームの境界を滑らかにしない 低負荷
of.options.SMOOTH_BIOMES.tooltip.4=周囲の色の平均をとって
of.options.SMOOTH_BIOMES.tooltip.5=バイオーム間の境界を滑らかにします。
of.options.SMOOTH_BIOMES.tooltip.6=草、葉、蔦、水に影響を与えます。

of.options.CONNECTED_TEXTURES=継ぎ目ないテクスチャ
of.options.CONNECTED_TEXTURES.tooltip.1=継ぎ目ないテクスチャ
of.options.CONNECTED_TEXTURES.tooltip.2=  オフ - テクスチャの継ぎ目をなくさない (初期値)
of.options.CONNECTED_TEXTURES.tooltip.3=  処理優先 - 処理優先でテクスチャの継ぎ目をなくす
of.options.CONNECTED_TEXTURES.tooltip.4=  描画優先 - 描画優先でテクスチャの継ぎ目をなくす
of.options.CONNECTED_TEXTURES.tooltip.5=草、砂岩、本棚で隣り合ったものを
of.options.CONNECTED_TEXTURES.tooltip.6=継ぎ目なく表示します。
of.options.CONNECTED_TEXTURES.tooltip.7=つながった状態のテクスチャは現在使用中の
of.options.CONNECTED_TEXTURES.tooltip.8=リソースパックに依存します。

of.options.NATURAL_TEXTURES=自然なテクスチャ
of.options.NATURAL_TEXTURES.tooltip.1=自然なテクスチャ
of.options.NATURAL_TEXTURES.tooltip.2=  オフ - 自然なテクスチャを用いない (初期値)
of.options.NATURAL_TEXTURES.tooltip.3=  オン - 自然なテクスチャを用いる
of.options.NATURAL_TEXTURES.tooltip.4=ブロックのテクスチャが同じ形を繰り返し、
of.options.NATURAL_TEXTURES.tooltip.5=格子状の模様が形成される現象を除去します。
of.options.NATURAL_TEXTURES.tooltip.6=これは基となるブロックのテクスチャを回転したり裏返したり
of.options.NATURAL_TEXTURES.tooltip.7=することで実現されます。自然なテクスチャの設定は
of.options.NATURAL_TEXTURES.tooltip.8=現在使用中のリソースパックに依存します。

of.options.EMISSIVE_TEXTURES=発光効果テクスチャ
of.options.EMISSIVE_TEXTURES.tooltip.1=発光効果テクスチャ
of.options.EMISSIVE_TEXTURES.tooltip.2=  オフ - 発光効果テクスチャを用いない (初期値)
of.options.EMISSIVE_TEXTURES.tooltip.3=  オン - 発光効果テクスチャを用いる
of.options.EMISSIVE_TEXTURES.tooltip.4=発光効果テクスチャは最大の明るさを持ったオーバーレイとして
of.options.EMISSIVE_TEXTURES.tooltip.5=描画されます。また、基となるテクスチャの
of.options.EMISSIVE_TEXTURES.tooltip.6=発光部分のシミュレーションにも用いられます。
of.options.EMISSIVE_TEXTURES.tooltip.7=発光効果テクスチャは現在使用中の
of.options.EMISSIVE_TEXTURES.tooltip.8=リソースパックに依存します。

of.options.CUSTOM_SKY=カスタムスカイ
of.options.CUSTOM_SKY.tooltip.1=カスタムスカイ
of.options.CUSTOM_SKY.tooltip.2=  オン - カスタムスカイを用いる (初期値) 高負荷
of.options.CUSTOM_SKY.tooltip.3=  オフ - 通常の空 低負荷
of.options.CUSTOM_SKY.tooltip.4=カスタムスカイ用のテクスチャは現在使用中の
of.options.CUSTOM_SKY.tooltip.5=リソースパックに依存します。

of.options.CUSTOM_ITEMS=カスタムアイテム
of.options.CUSTOM_ITEMS.tooltip.1=カスタムアイテム
of.options.CUSTOM_ITEMS.tooltip.2=  オン - カスタムアイテムを用いる (初期値) 高負荷
of.options.CUSTOM_ITEMS.tooltip.3=  オフ - 通常のアイテムテクスチャを用いる 低負荷
of.options.CUSTOM_ITEMS.tooltip.4=カスタムアイテム用のテクスチャは現在使用中の
of.options.CUSTOM_ITEMS.tooltip.5=リソースパックに依存します。

of.options.CUSTOM_ENTITY_MODELS=カスタムエンティティモデル
of.options.CUSTOM_ENTITY_MODELS.tooltip.1=カスタムエンティティモデル
of.options.CUSTOM_ENTITY_MODELS.tooltip.2=  オン - カスタムエンティティモデルを用いる (初期値) 、高負荷
of.options.CUSTOM_ENTITY_MODELS.tooltip.3=  オフ - 通常のエンティティモデル、低負荷
of.options.CUSTOM_ENTITY_MODELS.tooltip.4=カスタムエンティティモデルは現在使用中の
of.options.CUSTOM_ENTITY_MODELS.tooltip.5=リソースパックに依存します。

of.options.CUSTOM_GUIS=カスタムGUI
of.options.CUSTOM_GUIS.tooltip.1=カスタムGUI
of.options.CUSTOM_GUIS.tooltip.2=  オン - カスタムGUI (初期値) 、高負荷
of.options.CUSTOM_GUIS.tooltip.3=  オフ - 通常のGUI、低負荷
of.options.CUSTOM_GUIS.tooltip.4=カスタムGUIは現在使用中のリソースパックに依存します。

# Details

of.options.CLOUDS=雲
of.options.CLOUDS.tooltip.1=雲
of.options.CLOUDS.tooltip.2=  デフォルト - グラフィックスの設定に合わせる
of.options.CLOUDS.tooltip.3=  処理優先 - 低品質 低負荷
of.options.CLOUDS.tooltip.4=  描画優先 - 高品質 高負荷
of.options.CLOUDS.tooltip.5=  オフ - 雲なし 最低負荷
of.options.CLOUDS.tooltip.6=処理優先の雲は平面的に描画されます。
of.options.CLOUDS.tooltip.7=描画優先の雲は立体的に描画されます。

of.options.CLOUD_HEIGHT=雲の高さ
of.options.CLOUD_HEIGHT.tooltip.1=雲の高さ
of.options.CLOUD_HEIGHT.tooltip.2=  オフ - 通常の高さ
of.options.CLOUD_HEIGHT.tooltip.3=  100%% - ワールドの高さ上限

of.options.TREES=樹
of.options.TREES.tooltip.1=樹
of.options.TREES.tooltip.2=  デフォルト - グラフィックスの設定に合わせる
of.options.TREES.tooltip.3=  処理優先 - 低品質 最低負荷
of.options.TREES.tooltip.4=  スマート - 高品質 低負荷
of.options.TREES.tooltip.5=  描画優先 - 高品質 高負荷
of.options.TREES.tooltip.6=処理優先の木の葉は透過しません。
of.options.TREES.tooltip.7=描画優先や賢く描画された木の葉は透過します。

of.options.RAIN=雨と雪
of.options.RAIN.tooltip.1=雨と雪
of.options.RAIN.tooltip.2=  デフォルト - グラフィックスの設定に合わせる
of.options.RAIN.tooltip.3=  処理優先  - 小雨/小雪 低負荷
of.options.RAIN.tooltip.4=  描画優先 - 大雨/大雪 高負荷
of.options.RAIN.tooltip.5=  オフ - 雨や雪を描画しない 最低負荷
of.options.RAIN.tooltip.6=オフにしても地面での雨の飛び散りや雨音
of.options.RAIN.tooltip.7=は有効のままです。

of.options.SKY=空
of.options.SKY.tooltip.1=空
of.options.SKY.tooltip.2=  オン - 空を描画する 高負荷
of.options.SKY.tooltip.3=  オフ  - 空を描画しない 低負荷
of.options.SKY.tooltip.4=この設定をオフにしても太陽や月は表示されます。

of.options.STARS=星
of.options.STARS.tooltip.1=星
of.options.STARS.tooltip.2=  オン - 星を描画する (初期値)
of.options.STARS.tooltip.3=  オフ  - 星を描画しない (低負荷)

of.options.SUN_MOON=太陽と月
of.options.SUN_MOON.tooltip.1=太陽と月
of.options.SUN_MOON.tooltip.2=  オン - 太陽と月を描画する (初期値)
of.options.SUN_MOON.tooltip.3=  オフ  - 太陽と月を描画しない (低負荷)

of.options.SHOW_CAPES=マントの表示
of.options.SHOW_CAPES.tooltip.1=マントの表示
of.options.SHOW_CAPES.tooltip.2=  オン - マントを表示する (初期値)
of.options.SHOW_CAPES.tooltip.3=  オフ - マントを表示しない

of.options.TRANSLUCENT_BLOCKS=半透明のブロック
of.options.TRANSLUCENT_BLOCKS.tooltip.1=半透明のブロック
of.options.TRANSLUCENT_BLOCKS.tooltip.2=  デフォルト - グラフィックスの設定に合わせる
of.options.TRANSLUCENT_BLOCKS.tooltip.3=  描画優先 - 完全なカラーブレンド (初期値)
of.options.TRANSLUCENT_BLOCKS.tooltip.4=  処理優先 - 速さ重視のカラーブレンド (低負荷)
of.options.TRANSLUCENT_BLOCKS.tooltip.5=違う色を持った半透明ブロック (色付きガラス、水、氷)
of.options.TRANSLUCENT_BLOCKS.tooltip.6=が空気を挟んで隣接した際の
of.options.TRANSLUCENT_BLOCKS.tooltip.7=カラーブレンドの設定です。

of.options.HELD_ITEM_TOOLTIPS=持ち替え時アイテム名表示
of.options.HELD_ITEM_TOOLTIPS.tooltip.1=持ち替え時アイテム名表示
of.options.HELD_ITEM_TOOLTIPS.tooltip.2=  オン - 持ち替え時にアイテム名を表示する (初期値)
of.options.HELD_ITEM_TOOLTIPS.tooltip.3=  オフ - 持ち替え時にアイテム名を表示しない

of.options.ADVANCED_TOOLTIPS=高度なツールチップ
of.options.ADVANCED_TOOLTIPS.tooltip.1=高度なツールチップ
of.options.ADVANCED_TOOLTIPS.tooltip.2=  オン - 高度なツールチップを表示
of.options.ADVANCED_TOOLTIPS.tooltip.3=  オフ - 高度なツールチップを非表示 (初期値)
of.options.ADVANCED_TOOLTIPS.tooltip.4=高度なツールチップはアイテム (ID、耐久値) と
of.options.ADVANCED_TOOLTIPS.tooltip.5=シェーダーのオプション (ID、由来、初期値) において
of.options.ADVANCED_TOOLTIPS.tooltip.6=拡張された情報を表示します。

of.options.DROPPED_ITEMS=落ちているアイテムの表示
of.options.DROPPED_ITEMS.tooltip.1=落ちているアイテムの表示
of.options.DROPPED_ITEMS.tooltip.2=  デフォルト - グラフィックスの設定に合わせる
of.options.DROPPED_ITEMS.tooltip.3=  処理優先 - 落ちているアイテムを2Dで表示する (低負荷)
of.options.DROPPED_ITEMS.tooltip.4=  描画優先 - 落ちているアイテムを3Dで表示する (高負荷)

options.entityShadows.tooltip.1=エンティティの影
options.entityShadows.tooltip.2=  オン - エンティティの影を表示する
options.entityShadows.tooltip.3=  オフ - エンティティの影を表示しない

of.options.VIGNETTE=視界端の影
of.options.VIGNETTE.tooltip.1=スクリーンの角を少し暗くさせる視覚効果
of.options.VIGNETTE.tooltip.2=  デフォルト - グラフィックスの設定に合わせる (初期値)
of.options.VIGNETTE.tooltip.3=  処理優先 - 視界端の影を表示しない (低負荷)
of.options.VIGNETTE.tooltip.4=  描画優先 - 視界端の影を表示する (高負荷)
of.options.VIGNETTE.tooltip.5=視界端の影はFPSにとって重要な視覚効果です。
of.options.VIGNETTE.tooltip.6=例外としてフルスクリーンで遊ぶ際には、
of.options.VIGNETTE.tooltip.7=視界端の影は非常に微かであるため、
of.options.VIGNETTE.tooltip.8=無効にしても無害です。

of.options.DYNAMIC_FOV=臨場感ある視野角
of.options.DYNAMIC_FOV.tooltip.1=臨場感ある視野角
of.options.DYNAMIC_FOV.tooltip.2=  オン - 臨場感ある視野角を有効化 (初期値)
of.options.DYNAMIC_FOV.tooltip.3=  オフ - 臨場感ある視野角を無効化
of.options.DYNAMIC_FOV.tooltip.4=飛翔時や、ダッシュ時、弓の引き絞り時に、
of.options.DYNAMIC_FOV.tooltip.5=視野角 (FOV) を変えます。

of.options.DYNAMIC_LIGHTS=臨場感ある光源
of.options.DYNAMIC_LIGHTS.tooltip.1=臨場感ある光源
of.options.DYNAMIC_LIGHTS.tooltip.2=  オフ - 持っている光源を光らせない (初期値)
of.options.DYNAMIC_LIGHTS.tooltip.3=  処理優先 - 500ミリ秒ごとに位置を更新する
of.options.DYNAMIC_LIGHTS.tooltip.4=  描画優先 - リアルタイムに位置を更新する
of.options.DYNAMIC_LIGHTS.tooltip.5=光源となるアイテム (松明、グローストーンなど) について、
of.options.DYNAMIC_LIGHTS.tooltip.6=手に持っているか、他のプレイヤーが装備しているか、
of.options.DYNAMIC_LIGHTS.tooltip.7=地上に投げ出されている時に、周囲を照らします。

options.biomeBlendRadius.tooltip.1=バイオーム間の色の遷移を滑らかにする
options.biomeBlendRadius.tooltip.2=  オフ - 混じりなし (低負荷)
options.biomeBlendRadius.tooltip.3=  5x5 - 普通の混じり (初期値)
options.biomeBlendRadius.tooltip.4=  15x15 - 最大の混じり (高負荷)
options.biomeBlendRadius.tooltip.5=値が大きいと大きなラグが発生し、
options.biomeBlendRadius.tooltip.6=チャンクの読み込み速度が低下する可能性があります。

# Performance

of.options.SMOOTH_FPS=FPS安定化処理
of.options.SMOOTH_FPS.tooltip.1=バッファを使ってFPSを安定化する。
of.options.SMOOTH_FPS.tooltip.2=  オフ - 安定化しない。FPSは変動的になるでしょう。
of.options.SMOOTH_FPS.tooltip.3=  オン - 安定化する。
of.options.SMOOTH_FPS.tooltip.4=この設定はグラフィックドライバーに依存し、
of.options.SMOOTH_FPS.tooltip.5=常には有効ではありません。

of.options.SMOOTH_WORLD=サーバー負荷を分散
of.options.SMOOTH_WORLD.tooltip.1=内部サーバーによるラグの除去。
of.options.SMOOTH_WORLD.tooltip.2=  オフ - 安定化しない。FPSは変動的になるでしょう。
of.options.SMOOTH_WORLD.tooltip.3=  オン - 安定化する。
of.options.SMOOTH_WORLD.tooltip.4=内部サーバーロードを安定化。
of.options.SMOOTH_WORLD.tooltip.5=シングルプレイの時のみ効果があります。

of.options.FAST_RENDER=描画の最適化
of.options.FAST_RENDER.tooltip.1=描画の最適化
of.options.FAST_RENDER.tooltip.2= オフ - 通常の描画 (初期値) 
of.options.FAST_RENDER.tooltip.3= オン - 最適化された描画 (低負荷) 
of.options.FAST_RENDER.tooltip.4=GPUのロード回数を減らした方式によって
of.options.FAST_RENDER.tooltip.5=大幅にFPSを向上します。
of.options.FAST_RENDER.tooltip.6=この機能はModと競合する可能性があります。

of.options.FAST_MATH=計算の最適化
of.options.FAST_MATH.tooltip.1=計算の最適化
of.options.FAST_MATH.tooltip.2= オフ - 通常の計算 (初期値)
of.options.FAST_MATH.tooltip.3= オン - 最適化された計算
of.options.FAST_MATH.tooltip.4=最適化した三角関数を用いて
of.options.FAST_MATH.tooltip.5=CPUのキャッシュを活用しFPSを向上します。
of.options.FAST_MATH.tooltip.6=この機能は地形生成に僅かな影響を及ぼす可能性があります。

of.options.CHUNK_UPDATES=チャンク読込方法
of.options.CHUNK_UPDATES.tooltip.1=チャンク読込方法
of.options.CHUNK_UPDATES.tooltip.2= 1 - 高負荷なチャンク読み込み 高いFPS (初期値)
of.options.CHUNK_UPDATES.tooltip.3= 3 - 低負荷めなチャンク読み込み 低めのFPS
of.options.CHUNK_UPDATES.tooltip.4= 5 - 低負荷なチャンク読み込み 低いFPS
of.options.CHUNK_UPDATES.tooltip.5=1フレームごとに行われるチャンク読み込み回数を指定します。
of.options.CHUNK_UPDATES.tooltip.6=高い値はフレームレートの不安定化を招きます。

of.options.CHUNK_UPDATES_DYNAMIC=動的な読み込み
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.1=動的な読み込み
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.2= オフ - 1フレームに通常の回数のチャンク読み込み処理 (初期値)
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.3= オン - 静止中により多くの読み込む処理を行う
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.4=プレイヤーが静止している時に
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.5=素早くチャンク読み込むようになります。

of.options.LAZY_CHUNK_LOADING=チャンク読み込みの遅延
of.options.LAZY_CHUNK_LOADING.tooltip.1=チャンク読み込みの遅延
of.options.LAZY_CHUNK_LOADING.tooltip.2= オフ - 通常のサーバーチャンク読み込み
of.options.LAZY_CHUNK_LOADING.tooltip.3= オン - 遅延サーバーチャンク読み込み (滑らか)
of.options.LAZY_CHUNK_LOADING.tooltip.4=チャンク読み込み処理を数Tickに分散させることで
of.options.LAZY_CHUNK_LOADING.tooltip.5=統合サーバーのチャンク読み込み処理を滑らかにします。
of.options.LAZY_CHUNK_LOADING.tooltip.6=ワールドの一部に欠損が発生する場合はオフにしてください。
of.options.LAZY_CHUNK_LOADING.tooltip.7=ローカルワールド (シングルプレイヤー) の時のみ有効です。

of.options.RENDER_REGIONS=領域描画
of.options.RENDER_REGIONS.tooltip.1=領域描画
of.options.RENDER_REGIONS.tooltip.2= オフ - 領域描画を用いない (初期値)
of.options.RENDER_REGIONS.tooltip.3= オン - 領域描画を用いる
of.options.RENDER_REGIONS.tooltip.4=領域描画は描画距離が広い場合に素早い地形描画を可能にします。
of.options.RENDER_REGIONS.tooltip.5=頂点バッファオブジェクトが有効な場合に、より効果的です。
of.options.RENDER_REGIONS.tooltip.6=オンボードグラフィックカードでは推奨されません。

of.options.SMART_ANIMATIONS=スマートアニメーション
of.options.SMART_ANIMATIONS.tooltip.1=スマートアニメーション
of.options.SMART_ANIMATIONS.tooltip.2= オフ - スマートアニメーションを用いない (初期値)
of.options.SMART_ANIMATIONS.tooltip.3= オン - スマートアニメーションを用いる
of.options.SMART_ANIMATIONS.tooltip.4=スマートアニメーションを使えばテクスチャのアニメーション処理は
of.options.SMART_ANIMATIONS.tooltip.5=見える範囲でのみ行われるようになります。
of.options.SMART_ANIMATIONS.tooltip.6=ラグを減らし、FPSを増やすことができます。
of.options.SMART_ANIMATIONS.tooltip.7=特に、大きなModパックや高解像度のリソースパックに対してです。

# Animations

of.options.animation.allOn=全てオン
of.options.animation.allOff=全てオフ
of.options.animation.dynamic=動的

of.options.ANIMATED_WATER=水
of.options.ANIMATED_LAVA=溶岩
of.options.ANIMATED_FIRE=炎
of.options.ANIMATED_PORTAL=ネザーゲート
of.options.ANIMATED_REDSTONE=レッドストーン
of.options.ANIMATED_EXPLOSION=爆発
of.options.ANIMATED_FLAME=松明やかまど
of.options.ANIMATED_SMOKE=煙
of.options.VOID_PARTICLES=奈落のパーティクル
of.options.WATER_PARTICLES=水中のパーティクル
of.options.RAIN_SPLASH=雨の水しぶき
of.options.PORTAL_PARTICLES=ポータルのパーティクル
of.options.POTION_PARTICLES=ポーションのパーティクル
of.options.DRIPPING_WATER_LAVA=水/溶岩の滴り
of.options.ANIMATED_TERRAIN=地形アニメーション
of.options.ANIMATED_TEXTURES=テクスチャアニメーション
of.options.FIREWORK_PARTICLES=花火のパーティクル

# Other

of.options.LAGOMETER=ラグメーター
of.options.LAGOMETER.tooltip.1=デバッグ画面 (F3) にラグメーターを表示する。
of.options.LAGOMETER.tooltip.2=* 橙 - ガベージコレクション
of.options.LAGOMETER.tooltip.3=* 藍 - Tick処理
of.options.LAGOMETER.tooltip.4=* 青 - スケジューリングされた処理
of.options.LAGOMETER.tooltip.5=* 紫 - チャンクアップローディング
of.options.LAGOMETER.tooltip.6=* 赤 - チャンクアップデート
of.options.LAGOMETER.tooltip.7=* 黄 - 描画確認
of.options.LAGOMETER.tooltip.8=* 緑 - 地形描画

of.options.PROFILER=デバッグプロファイラ
of.options.PROFILER.tooltip.1=デバッグプロファイラ
of.options.PROFILER.tooltip.2=  オン - デバッグプロファイラは有効 高負荷
of.options.PROFILER.tooltip.3=  オフ - デバッグプロファイラは無効 低負荷
of.options.PROFILER.tooltip.4=デバッグ画面 (F3) が開かれている時に
of.options.PROFILER.tooltip.5=デバッグ情報を収集し円グラフ上に表示します。

of.options.WEATHER=天候
of.options.WEATHER.tooltip.1=天候
of.options.WEATHER.tooltip.2=  オン - 天候あり 高負荷
of.options.WEATHER.tooltip.3=  オフ - 天候なし 低負荷
of.options.WEATHER.tooltip.4=天候コントロール機能の設定を変更できます。
of.options.WEATHER.tooltip.5=シングルプレイでのみ利用可能な設定です。

of.options.time.dayOnly=昼のみ
of.options.time.nightOnly=夜のみ

of.options.TIME=クリエイティブの時刻
of.options.TIME.tooltip.1=クリエイティブの時刻
of.options.TIME.tooltip.2= デフォルト - 通常の昼夜サイクル
of.options.TIME.tooltip.3= 昼のみ - 昼のみ
of.options.TIME.tooltip.4= 夜のみ - 夜のみ
of.options.TIME.tooltip.5=シングルプレイ（ローカルワールド）の場合でかつ
of.options.TIME.tooltip.6=クリエイティブモードの場合に適用される設定です。

options.fullscreen.tooltip.1=フルスクリーン
options.fullscreen.tooltip.2=  オン - フルスクリーンモード
options.fullscreen.tooltip.3=  オフ - ウィンドウモード
options.fullscreen.tooltip.4=フルスクリーンでの処理の速さは
options.fullscreen.tooltip.5=グラフィックボードに左右されます。

options.fullscreen.resolution=フルスクリーンの種類
options.fullscreen.resolution.tooltip.1=フルスクリーンの種類
options.fullscreen.resolution.tooltip.2=  デフォルト - デスクトップスクリーンの解像度 高負荷
options.fullscreen.resolution.tooltip.3=  幅x高 - カスタム解像度 おそらく低負荷
options.fullscreen.resolution.tooltip.4=フルスクリーンモードにて使用される解像度です。
options.fullscreen.resolution.tooltip.5=低い解像度に設定した場合、通常FPSが向上します。

of.options.SHOW_FPS=FPSを表示
of.options.SHOW_FPS.tooltip.1=コンパクトなFPSと描画情報を表示
of.options.SHOW_FPS.tooltip.2=  FPS - 平均/最小
of.options.SHOW_FPS.tooltip.3=  C: - チャンク描画
of.options.SHOW_FPS.tooltip.4=  E: - エンティティ描画
of.options.SHOW_FPS.tooltip.5=  U: - チャンクアップデート
of.options.SHOW_FPS.tooltip.6=コンパクトなFPS情報は
of.options.SHOW_FPS.tooltip.7=デバッグ画面が非表示の時のみ表示されます。

of.options.save.45s=45秒
of.options.save.90s=90秒
of.options.save.3min=3分
of.options.save.6min=6分
of.options.save.12min=12分
of.options.save.24min=24分

of.options.AUTOSAVE_TICKS=オートセーブ
of.options.AUTOSAVE_TICKS.tooltip.1=セーブ間隔
of.options.AUTOSAVE_TICKS.tooltip.2= 45秒 - デフォルト
of.options.AUTOSAVE_TICKS.tooltip.3=描画距離によってはオートセーブはラグを引き起こします。
of.options.AUTOSAVE_TICKS.tooltip.4=ゲームメニューを開いた際にもワールドはセーブされます。

of.options.SCREENSHOT_SIZE=スクリーンショットのサイズ
of.options.SCREENSHOT_SIZE.tooltip.1=スクリーンショットのサイズ
of.options.SCREENSHOT_SIZE.tooltip.2=  デフォルト - 通常の大きさ
of.options.SCREENSHOT_SIZE.tooltip.3=  2x〜4x - 任意の大きさ
of.options.SCREENSHOT_SIZE.tooltip.4=より大きなスクリーンショットの撮影には、より多くのメモリが必要です。
of.options.SCREENSHOT_SIZE.tooltip.5=描画の最適化、アンチエイリアスとは共存できません。
of.options.SCREENSHOT_SIZE.tooltip.6=GPUがフレームバッファをサポートしている必要があります。

of.options.SHOW_GL_ERRORS=OpenGLエラーの表示
of.options.SHOW_GL_ERRORS.tooltip.1=OpenGLエラーの表示
of.options.SHOW_GL_ERRORS.tooltip.2=有効な場合、チャットにOpenGLのエラーが表示されます。
of.options.SHOW_GL_ERRORS.tooltip.3=既知の競合や修正不可能なエラーである場合に限り
of.options.SHOW_GL_ERRORS.tooltip.4=無効にしてください。
of.options.SHOW_GL_ERRORS.tooltip.5=無効にしてもエラーはエラーログに記載されるままであり
of.options.SHOW_GL_ERRORS.tooltip.6=大幅なFPSの下降の原因となり続けます。

# Chat Settings

of.options.CHAT_BACKGROUND=チャット欄
of.options.CHAT_BACKGROUND.tooltip.1=チャット欄
of.options.CHAT_BACKGROUND.tooltip.2=  デフォルト - 固定幅
of.options.CHAT_BACKGROUND.tooltip.3=  コンパクト - 行の幅に合わせる
of.options.CHAT_BACKGROUND.tooltip.4=  オフ - なし

of.options.CHAT_SHADOW=チャットの影
of.options.CHAT_SHADOW.tooltip.1=チャットの影
of.options.CHAT_SHADOW.tooltip.2=  オン - テキストに影をつける
of.options.CHAT_SHADOW.tooltip.3=  オフ - テキストに影をつけない
