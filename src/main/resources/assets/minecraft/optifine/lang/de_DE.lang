# Contributors of German localization #
#   ThexXTURBOXx (Collaborator of Reforged) 2016-2-29 ---- 2016-3-3
#   RoiEXLab 2016-3-8
#   violine1101 (German Minecraft Wiki admin) 2016-4-4 ---- 2017-6-7
#   shaderecker (TheHADILP) 2016-5-20 ---- 2019-10-16
#   Nerdyyy 2018-1-6
#   OfficialCRUGG / CRUGG 2019-03-17

# General
of.general.ambiguous=Unklar
of.general.compact=Kompakt
of.general.custom=Benutzerdefiniert
of.general.from=Von
of.general.id=ID
of.general.max=Maximum
of.general.restart=Neustart
of.general.smart=Intelligent

# Keys
of.key.zoom=Zoom

# Message
of.message.aa.shaders1=Antialiasing ist nicht mit Shadern kompatibel.
of.message.aa.shaders2=Bitte deaktiviere Shader, um diese Einstellung zu aktivieren.

of.message.af.shaders1=Anisotropische Filterung ist nicht mit Shadern kompatibel.
of.message.af.shaders2=Bitte deaktiviere Shader, um diese Einstellung zu aktivieren.

of.message.fr.shaders1=Schnelles Rendern ist nicht mit Shadern kompatibel.
of.message.fr.shaders2=Bitte deaktiviere Shader, um diese Einstellung zu aktivieren.

of.message.an.shaders1=3D-Effekt ist nicht mit Shadern kompatibel.
of.message.an.shaders2=Bitte deaktiviere Shader, um diese Einstellung zu aktivieren.

of.message.shaders.aa1=Antialiasing ist nicht mit Shadern kompatibel. 
of.message.shaders.aa2=Bitte deaktiviere Qualität -> Antialiasing und starte das Spiel neu.

of.message.shaders.af1=Anisotropische Filterung ist nicht mit Shadern kompatibel.
of.message.shaders.af2=Bitte deaktiviere Qualität -> Anisotropische Filterung.

of.message.shaders.fr1=Schnelles Rendern ist nicht mit Shadern kompatibel.
of.message.shaders.fr2=Bitte deaktiviere Leistung -> Schnelles Rendern.

of.message.shaders.an1=Shader sind nicht mit dem 3D-Effekt kompatibel.
of.message.shaders.an2=Bitte deaktiviere Sonstiges -> 3D-Effekt.

of.message.shaders.nv1=Dieses Shader-Paket erfordert eine neuere OptiFine Version: %s
of.message.shaders.nv2=Willst du wirklich fortfahren?

of.message.newVersion=Eine neue Version von §eOptiFine§f ist verfügbar: §e%s§f
of.message.java64Bit=Du kannst die §e64-bit-Version von Java§f installieren, um die Leistung zu verbessern
of.message.openglError=§eOpenGL-Fehler§f: %s (%s)

of.message.shaders.loading=Lade Shader: %s

of.message.other.reset=Alle Grafikeinstellungen auf die Standardwerte zurücksetzen?

of.message.loadingVisibleChunks=Lade sichtbare Chunks

# Skin customization

of.options.skinCustomisation.ofCape=OptiFine-Umhang …

of.options.capeOF.title=OptiFine-Umhang
of.options.capeOF.openEditor=Umhang-Editor öffnen
of.options.capeOF.reloadCape=Umhang neu laden

of.message.capeOF.openEditor=Der OptiFine-Umhang-Editor sollte sich in einem Web-Browser öffnen.
of.message.capeOF.reloadCape=Dein Umhang wird in 15 Sekunden neu geladen.

of.message.capeOF.error1=Mojang-Authentifizierung fehlgeschlagen.
of.message.capeOF.error2=Fehler: %s
 
# Video settings

options.graphics.tooltip.1=Grafikmodus
options.graphics.tooltip.2=  Schnell - Niedrigere Qualität (schneller)
options.graphics.tooltip.3=  Schön   - Höhere Qualität (langsamer)
options.graphics.tooltip.4=Verändert das Aussehen von Wolken, Blättern, Wasser,
options.graphics.tooltip.5=Schatten und Grasblöcken.

of.options.renderDistance.tiny=Winzig
of.options.renderDistance.short=Klein
of.options.renderDistance.normal=Normal
of.options.renderDistance.far=Weit
of.options.renderDistance.extreme=Extrem
of.options.renderDistance.insane=Wahnsinnig
of.options.renderDistance.ludicrous=Absurd

options.renderDistance.tooltip.1=Sichtweite
options.renderDistance.tooltip.2=  2 Winzig - 32m (am schnellsten)
options.renderDistance.tooltip.3=  8 Normal - 128m
options.renderDistance.tooltip.4=  16 Weit - 256m (langsam)
options.renderDistance.tooltip.5=  32 Extrem - 512m (am langsamsten!) ressourcenlastig
options.renderDistance.tooltip.6=  48 Wahnsinnig - 768m, braucht 2GB RAM zugeteilt
options.renderDistance.tooltip.7=  64 Absurd - 1024m, braucht 3GB RAM zugeteilt
options.renderDistance.tooltip.8=Werte über 16 funktionieren nur im Einzelspielermodus.

options.ao.tooltip.1=Weiche Beleuchtung
options.ao.tooltip.2=  Aus - Normale Beleuchtung (schnell)
options.ao.tooltip.3=  Minimum - Einfache weiche Beleuchtung (langsam)
options.ao.tooltip.4=  Maximum - Komplexe weiche Beleuchtung (am langsamsten)

options.framerateLimit.tooltip.1=Maximale Bildrate
options.framerateLimit.tooltip.2=  V-Sync - Auf Monitor-Bildrate begrenzen (60, 30, 20)
options.framerateLimit.tooltip.3=  5-255 - Auf eingestellte Bildrate begrenzen
options.framerateLimit.tooltip.4=  Unendlich - Keine Begrenzung (am schnellsten)
options.framerateLimit.tooltip.5=Die Bildrate grenzt die FPS ein, sogar wenn
options.framerateLimit.tooltip.6=die Begrenzung nicht erreicht ist.
of.options.framerateLimit.vsync=V-Sync

of.options.AO_LEVEL=Schattenhelligkeit
of.options.AO_LEVEL.tooltip.1=Schattenhelligkeit
of.options.AO_LEVEL.tooltip.2=  Aus - Keine Schatten
of.options.AO_LEVEL.tooltip.3=  50%% - Helle Schatten
of.options.AO_LEVEL.tooltip.4=  100%% - Dunkle Schatten

options.viewBobbing.tooltip.1=Gehbewegung
options.viewBobbing.tooltip.2=Wenn Mipmaps verwendet werden, deaktiviere diese
options.viewBobbing.tooltip.3=Einstellung für bessere Leitung.

options.guiScale.tooltip.1=GUI-Größe
options.guiScale.tooltip.2=  Auto - Maximale Größe
options.guiScale.tooltip.3=  Klein, Normal, Groß - 1x-3x
options.guiScale.tooltip.4=  4x-10x - Verfügbar auf 4K-Monitoren
options.guiScale.tooltip.5=Ungerade Werte (1x, 3x, 5x …) sind nicht mit
options.guiScale.tooltip.6=Unicode-Schrift kompatibel.
options.guiScale.tooltip.7=Ein kleineres GUI ist eventuell schneller.

options.vbo=VBOs verwenden
options.vbo.tooltip.1=Vertexbufferobjekte
options.vbo.tooltip.2=Benutzt ein alternatives Rendermodell, das normalerweise
options.vbo.tooltip.3=schneller (5-10%%) als das Standard-Rendermodell ist.

options.gamma.tooltip.1=Ändert die Helligkeit dunkler Objekte
options.gamma.tooltip.2=  Düster - Standardhelligkeit
options.gamma.tooltip.3=  1-99%% - veränderlich
options.gamma.tooltip.4=  Hell - Maximale Helligkeit für dunkle Objekte
options.gamma.tooltip.5=Diese Einstellung ändert nicht die Helligkeit von komplett
options.gamma.tooltip.6=schwarzen Objekten.

options.anaglyph.tooltip.1=3D-Effekt
options.anaglyph.tooltip.2=Aktiviert einen stereoskopischen 3D-Effekt durch
options.anaglyph.tooltip.3=Verwendung unterschiedlicher Farben für jedes Auge.
options.anaglyph.tooltip.4=Kann nur mit einer 3D-Brille benutzt werden.

of.options.ALTERNATE_BLOCKS=Blockvarianten
of.options.ALTERNATE_BLOCKS.tooltip.1=Blockvarianten
of.options.ALTERNATE_BLOCKS.tooltip.2=Benutzt alternative Blockmodelle für einige Blöcke.
of.options.ALTERNATE_BLOCKS.tooltip.3=Abhängig vom ausgewählten Ressourcenpaket.

of.options.FOG_FANCY=Nebel
of.options.FOG_FANCY.tooltip.1=Nebel
of.options.FOG_FANCY.tooltip.2=  Schnell - Schneller Nebel
of.options.FOG_FANCY.tooltip.3=  Schön - Langsamer Nebel, sieht besser aus
of.options.FOG_FANCY.tooltip.4=  Aus - Kein Nebel, am schnellsten
of.options.FOG_FANCY.tooltip.5=Der schöne Nebel ist nur verfügbar, wenn er von der
of.options.FOG_FANCY.tooltip.6=Grafikkarte unterstützt wird.

of.options.FOG_START=Nebelstart
of.options.FOG_START.tooltip.1=Nebelstart
of.options.FOG_START.tooltip.2=  0.2 - Der Nebel startet nahe beim Spieler
of.options.FOG_START.tooltip.3=  0.8 - Der Nebel startet weit weg vom Spieler
of.options.FOG_START.tooltip.4=Diese Einstellung beeinflusst normalerweise nicht die
of.options.FOG_START.tooltip.5=Leistung.

of.options.CHUNK_LOADING=Chunkladen
of.options.CHUNK_LOADING.tooltip.1=Chunkladen
of.options.CHUNK_LOADING.tooltip.2=  Standard - Instabile FPS, wenn Chunks geladen werden
of.options.CHUNK_LOADING.tooltip.3=  Weich - Stabile FPS
of.options.CHUNK_LOADING.tooltip.4=  Multi-Core - Stabile FPS, 3x schnelleres Weltladen
of.options.CHUNK_LOADING.tooltip.5=Weich und Multi-Core entfernen Ruckler und
of.options.CHUNK_LOADING.tooltip.6=Standbilder, welche durch Chunkladen verursacht werden.
of.options.CHUNK_LOADING.tooltip.7=Multi-Core kann das Weltladen 3x schneller machen und
of.options.CHUNK_LOADING.tooltip.8=die FPS erhöhen, indem es einen zweiten CPU-Kern benutzt.
of.options.chunkLoading.smooth=Weich
of.options.chunkLoading.multiCore=Multi-Core

of.options.shaders=Shader …
of.options.shadersTitle=Shader

of.options.shaders.packNone=Aus
of.options.shaders.packDefault=(Interner Shader)

of.options.shaders.ANTIALIASING=Antialiasing
of.options.shaders.ANTIALIASING.tooltip.1=Antialiasing
of.options.shaders.ANTIALIASING.tooltip.2=  Aus - (Standard) kein Antialiasing (schneller)
of.options.shaders.ANTIALIASING.tooltip.3=  FXAA 2x, 4x - Geglättete Linien und Ränder (langsamer)
of.options.shaders.ANTIALIASING.tooltip.4=FXAA ist ein Nachbearbeitungs-Effekt, der zackige
of.options.shaders.ANTIALIASING.tooltip.5=Linien und harte Farbübergänge glättet.
of.options.shaders.ANTIALIASING.tooltip.6=Dies ist schneller als herkömmliches Antialiasing
of.options.shaders.ANTIALIASING.tooltip.7=und kompatibel mit Shadern und schnellem Rendern.

of.options.shaders.NORMAL_MAP=Normalkarten
of.options.shaders.NORMAL_MAP.tooltip.1=Normalkarten
of.options.shaders.NORMAL_MAP.tooltip.2=  An - (Standard) Normalkarten aktiviert
of.options.shaders.NORMAL_MAP.tooltip.3=  Aus - Normalkarten deaktiviert
of.options.shaders.NORMAL_MAP.tooltip.4=Normalkarten können von Shader-Paketen verwendet
of.options.shaders.NORMAL_MAP.tooltip.5=werden, um 3D-Geometrie auf flachen Oberflächen zu
of.options.shaders.NORMAL_MAP.tooltip.6=simulieren.
of.options.shaders.NORMAL_MAP.tooltip.7=Die Normalkarten werden vom aktivierten
of.options.shaders.NORMAL_MAP.tooltip.8=Ressourcenpaket zur Verfügung gestellt.

of.options.shaders.SPECULAR_MAP=Reflexionskarten
of.options.shaders.SPECULAR_MAP.tooltip.1=Reflexionskarten
of.options.shaders.SPECULAR_MAP.tooltip.2=  An - (Standard) Reflexionskarten aktiviert
of.options.shaders.SPECULAR_MAP.tooltip.3=  Aus - Reflexionskarten deaktiviert
of.options.shaders.SPECULAR_MAP.tooltip.4=Reflexionskarten können von Shader-Paketen verwendet
of.options.shaders.SPECULAR_MAP.tooltip.5=werden, um spezielle Reflexionseffekte zu erzeugen.
of.options.shaders.SPECULAR_MAP.tooltip.6=Die Reflexionskarten werden vom aktivierten
of.options.shaders.SPECULAR_MAP.tooltip.7=Ressourcenpaket zur Verfügung gestellt.

of.options.shaders.RENDER_RES_MUL=Renderqualität
of.options.shaders.RENDER_RES_MUL.tooltip.1=Renderqualität
of.options.shaders.RENDER_RES_MUL.tooltip.2=  0.5x - Niedrig (am schnellsten)
of.options.shaders.RENDER_RES_MUL.tooltip.3=  1x - Standard
of.options.shaders.RENDER_RES_MUL.tooltip.4=  2x - Hoch (am langsamsten)
of.options.shaders.RENDER_RES_MUL.tooltip.5=Die Renderqualität regelt die Auflösung der Textur,
of.options.shaders.RENDER_RES_MUL.tooltip.6=auf welcher die Shader gerendert werden.
of.options.shaders.RENDER_RES_MUL.tooltip.7=Niedrigere Werte können bei 4K-Monitoren nützlich sein.
of.options.shaders.RENDER_RES_MUL.tooltip.8=Höhere Werte arbeiten wie ein Antialiasing-Filter.

of.options.shaders.SHADOW_RES_MUL=Schattenqualität
of.options.shaders.SHADOW_RES_MUL.tooltip.1=Schattenqualität
of.options.shaders.SHADOW_RES_MUL.tooltip.2=  0.5x - Niedrig (am schnellsten)
of.options.shaders.SHADOW_RES_MUL.tooltip.3=  1x - Standard
of.options.shaders.SHADOW_RES_MUL.tooltip.4=  2x - Hoch (am langsamsten)
of.options.shaders.SHADOW_RES_MUL.tooltip.5=Die Schattenqualität regelt die Auflösung der Schatten-
of.options.shaders.SHADOW_RES_MUL.tooltip.6=Karten Textur des benutzten Shader-Pakets.
of.options.shaders.SHADOW_RES_MUL.tooltip.7=Niedrigere Werte = Ungenaue, grobe Schatten.
of.options.shaders.SHADOW_RES_MUL.tooltip.8=Höhere Werte = Detaillierte, feine Schatten.

of.options.shaders.HAND_DEPTH_MUL=Handtiefe
of.options.shaders.HAND_DEPTH_MUL.tooltip.1=Handtiefe
of.options.shaders.HAND_DEPTH_MUL.tooltip.2=  0.5x - Hand in Kamera-Nähe
of.options.shaders.HAND_DEPTH_MUL.tooltip.3=  1x - (Standard)
of.options.shaders.HAND_DEPTH_MUL.tooltip.4=  2x - Hand weiter von der Kamera entfernt
of.options.shaders.HAND_DEPTH_MUL.tooltip.5=Die Handtiefe steuert, wie weit Gegenstände von
of.options.shaders.HAND_DEPTH_MUL.tooltip.6=der Kamera entfernt sind.
of.options.shaders.HAND_DEPTH_MUL.tooltip.7=Für Shader-Pakete mit Tiefenunschärfe regelt dies
of.options.shaders.HAND_DEPTH_MUL.tooltip.8=die Unschärfe der tragbaren Gegenstände.

of.options.shaders.CLOUD_SHADOW=Wolkenschatten

of.options.shaders.OLD_HAND_LIGHT=Alte Handbel.
of.options.shaders.OLD_HAND_LIGHT.tooltip.1=Alte Handbeleuchtung
of.options.shaders.OLD_HAND_LIGHT.tooltip.2=  Standard - gesteuert vom Shader-Paket
of.options.shaders.OLD_HAND_LIGHT.tooltip.3=  An - Alte Handbeleuchtung aktiviert
of.options.shaders.OLD_HAND_LIGHT.tooltip.4=  Aus - Alte Handbeleuchtung deaktiviert
of.options.shaders.OLD_HAND_LIGHT.tooltip.5=Die alte Handbeleuchtung sorgt dafür, dass leuchtende
of.options.shaders.OLD_HAND_LIGHT.tooltip.6=Gegenstände bei alten Shader-Paketen sowohl in der
of.options.shaders.OLD_HAND_LIGHT.tooltip.7=Haupt-, als auch der Nebenhand erkannt werden.

of.options.shaders.OLD_LIGHTING=Alte Bel.
of.options.shaders.OLD_LIGHTING.tooltip.1=Alte Beleuchtung
of.options.shaders.OLD_LIGHTING.tooltip.2=  Standard - gesteuert vom Shader-Paket
of.options.shaders.OLD_LIGHTING.tooltip.3=  An - Alte Beleuchtung aktiviert
of.options.shaders.OLD_LIGHTING.tooltip.4=  Aus - Alte Beleuchtung deaktiviert
of.options.shaders.OLD_LIGHTING.tooltip.5=Die alte Beleuchtung steuert die starre "Vanilla"
of.options.shaders.OLD_LIGHTING.tooltip.6=Beleuchtung der Block-Seiten.
of.options.shaders.OLD_LIGHTING.tooltip.7=Shader-Pakete mit Schatten variierend nach Sonnenstand
of.options.shaders.OLD_LIGHTING.tooltip.8=stellen meistens eine bessere Beleuchtung zur Verfügung.

of.options.shaders.DOWNLOAD=Shader von Website herunterladen
of.options.shaders.DOWNLOAD.tooltip.1=Shader von Website herunterladen
of.options.shaders.DOWNLOAD.tooltip.2= 
of.options.shaders.DOWNLOAD.tooltip.3=Öffnet die Shader-Paket Website im Browser.
of.options.shaders.DOWNLOAD.tooltip.4=Lege das heruntergeladene Shader-Paket in den
of.options.shaders.DOWNLOAD.tooltip.5=Shader-Ordner, damit es in der Liste der
of.options.shaders.DOWNLOAD.tooltip.6=installierten Shader erscheint.

of.options.shaders.SHADER_PACK=Shader

of.options.shaders.shadersFolder=Shader-Ordner
of.options.shaders.shaderOptions=Shadereinstellungen …

of.options.shaderOptionsTitle=Shader

of.options.quality=Qualität …
of.options.qualityTitle=Qualitätseinstellungen

of.options.details=Details …
of.options.detailsTitle=Detaileinstellungen

of.options.performance=Leistung …
of.options.performanceTitle=Leistungseinstellungen

of.options.animations=Animationen …
of.options.animationsTitle=Animationseinstellungen

of.options.other=Sonstiges …
of.options.otherTitle=Andere Einstellungen

of.options.other.reset=Grafikeinstellungen zurücksetzen …

of.shaders.profile=Profil

# Quality

of.options.mipmap.bilinear=Bilinear
of.options.mipmap.linear=Linear
of.options.mipmap.nearest=Am nächsten
of.options.mipmap.trilinear=Trilinear

options.mipmapLevels.tooltip.1=Visueller Effekt, der weit entfernte Objekte besser aus-
options.mipmapLevels.tooltip.2=sehen lässt, indem Texturdetails geglättet werden.
options.mipmapLevels.tooltip.3=  Aus - Keine Glättung von Details
options.mipmapLevels.tooltip.4=  1 - Minimale Glättung von Details
options.mipmapLevels.tooltip.5=  Maximum - Maximale Glättung von Details
options.mipmapLevels.tooltip.6=Diese Einstellung beeinflusst normalerweise nicht die
options.mipmapLevels.tooltip.7=Leistung.

of.options.MIPMAP_TYPE=Mipmap-Typ
of.options.MIPMAP_TYPE.tooltip.1=Visueller Effekt, der weit entfernte Objekte besser aus-
of.options.MIPMAP_TYPE.tooltip.2=sehen lässt, indem Texturdetails geglättet werden.
of.options.MIPMAP_TYPE.tooltip.3=  Am nächsten - Grobe Glättung (am schnellsten)
of.options.MIPMAP_TYPE.tooltip.4=  Linear - Normale Glättung
of.options.MIPMAP_TYPE.tooltip.5=  Bilinear - Feine Glättung
of.options.MIPMAP_TYPE.tooltip.6=  Trilinear - Feinste Glättung (am langsamsten)


of.options.AA_LEVEL=Antialiasing
of.options.AA_LEVEL.tooltip.1=Antialiasing
of.options.AA_LEVEL.tooltip.2= Aus - (Standard) Kein Antialiasing (schneller)
of.options.AA_LEVEL.tooltip.3= 2-16 - Antialiasierte Linien und Ecken (langsamer)
of.options.AA_LEVEL.tooltip.4=Das Antialiasing weicht gezackte Linien ab und schärft
of.options.AA_LEVEL.tooltip.5=Farbübergänge. Wenn aktiviert, kann es die Bildrate
of.options.AA_LEVEL.tooltip.6=erheblich beeinträchtigen.
of.options.AA_LEVEL.tooltip.7=Nicht alle Stufen werden von allen Grafikkarten unterstützt.
of.options.AA_LEVEL.tooltip.8=Änderung wird erst nach einem Neustart effektiv!

of.options.AF_LEVEL=Anisotropische Filterung
of.options.AF_LEVEL.tooltip.1=Anisotropische Filterung
of.options.AF_LEVEL.tooltip.2= Aus - (Standard) Standard-Texturdetails (schneller)
of.options.AF_LEVEL.tooltip.3= 2-16 - Feinere Details in Texturen (langsamer)
of.options.AF_LEVEL.tooltip.4=Die anisotropische Filterung stellt Details in Texturen,
of.options.AF_LEVEL.tooltip.5=die durch Mipmap ihre Details verloren haben, wieder her.
of.options.AF_LEVEL.tooltip.6=Wenn aktiviert, kann es die FPS erheblich verringern.

of.options.CLEAR_WATER=Klares Wasser
of.options.CLEAR_WATER.tooltip.1=Klares Wasser
of.options.CLEAR_WATER.tooltip.2=  An - Klares, transparentes Wasser
of.options.CLEAR_WATER.tooltip.3=  Aus - Normales Wasser

of.options.RANDOM_ENTITIES=Entitätsvarianten
of.options.RANDOM_ENTITIES.tooltip.1=Entitätsvarianten
of.options.RANDOM_ENTITIES.tooltip.2=  Aus - Keine Entitätsvarianten (schneller)
of.options.RANDOM_ENTITIES.tooltip.3=  An - Entitätsvarianten (langsamer)
of.options.RANDOM_ENTITIES.tooltip.4=Die gleichen Entitäten können unterschiedliche Texturen
of.options.RANDOM_ENTITIES.tooltip.5=haben. Dies benötigt ein Ressourcenpaket mit entspre-
of.options.RANDOM_ENTITIES.tooltip.6=chenden Texturen.

of.options.BETTER_GRASS=Besseres Gras
of.options.BETTER_GRASS.tooltip.1=Besseres Gras
of.options.BETTER_GRASS.tooltip.2=  Aus - Standard-Grasseitentextur, am schnellsten
of.options.BETTER_GRASS.tooltip.3=  Schnell - Volle Grasseitentextur, langsamer
of.options.BETTER_GRASS.tooltip.4=  Schön - Dynamische Grasseitentextur, am langsamsten

of.options.BETTER_SNOW=Besserer Schnee
of.options.BETTER_SNOW.tooltip.1=Besserer Schnee
of.options.BETTER_SNOW.tooltip.2=  Aus - Normaler Schnee (schneller)
of.options.BETTER_SNOW.tooltip.3=  An - Besserer Schnee (langsamer)
of.options.BETTER_SNOW.tooltip.4=Setzt Schnee unter transparente Blöcke (wie Zäune
of.options.BETTER_SNOW.tooltip.5=oder hohes Gras), wenn sie an Schneeblöcke grenzen.

of.options.CUSTOM_FONTS=Schriftartressourcen
of.options.CUSTOM_FONTS.tooltip.1=Schriftartressourcen
of.options.CUSTOM_FONTS.tooltip.2=  An - Ressourcenpaket-Schriftart (Standard, langsamer)
of.options.CUSTOM_FONTS.tooltip.3=  Aus - Standardschriftart (schneller)
of.options.CUSTOM_FONTS.tooltip.4=Die Schriftart wird aus den aktivierten
of.options.CUSTOM_FONTS.tooltip.5=Ressourcenpaketen geladen.

of.options.CUSTOM_COLORS=Farbressourcen
of.options.CUSTOM_COLORS.tooltip.1=Farbressourcen
of.options.CUSTOM_COLORS.tooltip.2=  An - Ressourcenpaket-Farben (Standard, langsamer)
of.options.CUSTOM_COLORS.tooltip.3=  Aus - Standardfarben (schneller)
of.options.CUSTOM_COLORS.tooltip.4=Die Farben werden aus den aktivierten
of.options.CUSTOM_COLORS.tooltip.5=Ressourcenpaketen geladen.

of.options.SWAMP_COLORS=Sumpffarben
of.options.SWAMP_COLORS.tooltip.1=Sumpffarben
of.options.SWAMP_COLORS.tooltip.2=  An - Färbt den Sumpf dunkler (Standard, langsamer)
of.options.SWAMP_COLORS.tooltip.3=  Aus - Färbt den Sumpf in normalen Farben (schneller)
of.options.SWAMP_COLORS.tooltip.4=Die Sumpffarben betreffen Gras, Laub, Ranken und
of.options.SWAMP_COLORS.tooltip.5=Wasser.

of.options.SMOOTH_BIOMES=Biomübergänge
of.options.SMOOTH_BIOMES.tooltip.1=Biomübergänge
of.options.SMOOTH_BIOMES.tooltip.2=  An - Flüssige Biomübergänge (Standard, langsamer)
of.options.SMOOTH_BIOMES.tooltip.3=  Aus - Keine flüssigen Biomübergänge (schneller)
of.options.SMOOTH_BIOMES.tooltip.4=Die Farbübergänge werden durch Probennahme und
of.options.SMOOTH_BIOMES.tooltip.5=Durchschnittsberechnung der Farben der umliegenden
of.options.SMOOTH_BIOMES.tooltip.6=Blöcke berechnet. Betroffen sind Gras, Laub, Ranken und
of.options.SMOOTH_BIOMES.tooltip.7=Wasser.

of.options.CONNECTED_TEXTURES=Verbundene Texturen
of.options.CONNECTED_TEXTURES.tooltip.1=Verbundene Texturen
of.options.CONNECTED_TEXTURES.tooltip.2=  Aus - Keine verbundenen Texturen (Standard)
of.options.CONNECTED_TEXTURES.tooltip.3=  Schnell - Schnelle verbundene Texturen
of.options.CONNECTED_TEXTURES.tooltip.4=  Schöne - Schöne verbundene Texturen
of.options.CONNECTED_TEXTURES.tooltip.5=Verbundene Texturen verbinden die Texturen von Glas,
of.options.CONNECTED_TEXTURES.tooltip.6=Sandstein und Bücherregalen, wenn sie nebeneinander
of.options.CONNECTED_TEXTURES.tooltip.7=platziert werden. Die verbundenen Texturen werden aus
of.options.CONNECTED_TEXTURES.tooltip.8=den aktivierten Ressourcenpaketen geladen.

of.options.NATURAL_TEXTURES=Natürliche Texturen
of.options.NATURAL_TEXTURES.tooltip.1=Natürliche Texturen
of.options.NATURAL_TEXTURES.tooltip.2=  Aus - Keine natürlichen Texturen (Standard)
of.options.NATURAL_TEXTURES.tooltip.3=  An - Benutzt natürliche Texturen
of.options.NATURAL_TEXTURES.tooltip.4=Natürliche Texturen entfernen die rasterartige
of.options.NATURAL_TEXTURES.tooltip.5=Anordnung beim Platzieren gleicher Blöcke
of.options.NATURAL_TEXTURES.tooltip.6=durch Variationen der Standard-Blocktextur.
of.options.NATURAL_TEXTURES.tooltip.7=Die Einstellungen für die natürlichen Texturen
of.options.NATURAL_TEXTURES.tooltip.8=werden aus den aktivierten Ressourcenpaketen geladen.

of.options.EMISSIVE_TEXTURES=Leuchtende Texturen
of.options.EMISSIVE_TEXTURES.tooltip.1=Leuchtende Texturen
of.options.EMISSIVE_TEXTURES.tooltip.2=  Aus - Keine lichtemittierenden Texturen (Standard)
of.options.EMISSIVE_TEXTURES.tooltip.3=  An - Lichtemittierende Texturen
of.options.EMISSIVE_TEXTURES.tooltip.4=Die lichtemittierenden Texturen werden als Overlays mit
of.options.EMISSIVE_TEXTURES.tooltip.5=voller Helligkeit gerendert. Dadurch können leuchtende
of.options.EMISSIVE_TEXTURES.tooltip.6=Bereiche der Grund-Textur simuliert werden.
of.options.EMISSIVE_TEXTURES.tooltip.7=Die lichtemittierenden Texturen werden aus den
of.options.EMISSIVE_TEXTURES.tooltip.8=aktivierten Ressourcenpaketen geladen.

of.options.CUSTOM_SKY=Himmelstexturen
of.options.CUSTOM_SKY.tooltip.1=Himmelstexturen
of.options.CUSTOM_SKY.tooltip.2=  An - Ressourcenpaket-Himmel (Standard, langsam)
of.options.CUSTOM_SKY.tooltip.3=  Aus - Standardhimmel (schneller)
of.options.CUSTOM_SKY.tooltip.4=Die Himmelstexturen werden aus den aktivierten
of.options.CUSTOM_SKY.tooltip.5=Ressourcenpaketen geladen.

of.options.CUSTOM_ITEMS=Gegenstandstexturen
of.options.CUSTOM_ITEMS.tooltip.1=Gegenstandstexturen
of.options.CUSTOM_ITEMS.tooltip.2=  An - Ressourcenpaket-G.texturen (Standard, langsam)
of.options.CUSTOM_ITEMS.tooltip.3=  Aus - Standard-Gegenstandstexturen (schneller)
of.options.CUSTOM_ITEMS.tooltip.4=Die Gegenstandstexturen werden aus den aktivierten
of.options.CUSTOM_ITEMS.tooltip.5=Ressourcenpaketen geladen.

of.options.CUSTOM_ENTITY_MODELS=Wesens- & Objektmodelle
of.options.CUSTOM_ENTITY_MODELS.tooltip.1=Wesens- und Objektmodelle
of.options.CUSTOM_ENTITY_MODELS.tooltip.2=  An - Ressourcenpaketmodelle (Standard, langsam)
of.options.CUSTOM_ENTITY_MODELS.tooltip.3=  Aus - Standardmodelle (schneller)
of.options.CUSTOM_ENTITY_MODELS.tooltip.4=Die Wesens- und Objektmodelle werden aus den
of.options.CUSTOM_ENTITY_MODELS.tooltip.5=aktivierten Ressourcenpaketen geladen.

of.options.CUSTOM_GUIS=Ressourcenpaket-GUI
of.options.CUSTOM_GUIS.tooltip.1=Ressourcenpaket-GUI
of.options.CUSTOM_GUIS.tooltip.2=  An - Ressourcenpaket-GUI (Standard, langsam)
of.options.CUSTOM_GUIS.tooltip.3=  Aus - Standard GUI (schneller)
of.options.CUSTOM_GUIS.tooltip.4=Das GUI wird aus den aktivierten Ressourcenpaketen
of.options.CUSTOM_GUIS.tooltip.5=geladen.

# Details

of.options.CLOUDS=Wolken
of.options.CLOUDS.tooltip.1=Wolken
of.options.CLOUDS.tooltip.2=  Standard - Wie Grafikmodus
of.options.CLOUDS.tooltip.3=  Schnell - Schlechtere Qualität (schneller)
of.options.CLOUDS.tooltip.4=  Schön - Höhere Qualität (langsamer)
of.options.CLOUDS.tooltip.5=  Aus - Keine Wolken (am schnellsten)
of.options.CLOUDS.tooltip.6=Schnelle Wolken werden zweidimensional dargestellt.
of.options.CLOUDS.tooltip.7=Schöne Wolken werden dreidimensional dargestellt.

of.options.CLOUD_HEIGHT=Wolkenhöhe
of.options.CLOUD_HEIGHT.tooltip.1=Wolkenhöhe
of.options.CLOUD_HEIGHT.tooltip.2=  Aus - Standardhöhe
of.options.CLOUD_HEIGHT.tooltip.3=  100%% - Über der maximalen Welthöhe

of.options.TREES=Bäume
of.options.TREES.tooltip.1=Bäume
of.options.TREES.tooltip.2=  Standard - Wie Grafikmodus
of.options.TREES.tooltip.3=  Schnell - Schlechtere Qualität (schneller)
of.options.TREES.tooltip.4=  Fein - Hohe Qualität (schnell)
of.options.TREES.tooltip.5=  Schön - Höchste Qualität (langsamer)
of.options.TREES.tooltip.6=Schnelle Bäume haben solide Blätter.
of.options.TREES.tooltip.7=Schöne Bäume haben teilweise transparente Blätter.

of.options.RAIN=Regen & Schnee
of.options.RAIN.tooltip.1=Regen & Schnee
of.options.RAIN.tooltip.2=  Standard - Wie Grafikmodus
of.options.RAIN.tooltip.3=  Schnell - Leichter Regen/Schnee (schneller)
of.options.RAIN.tooltip.4=  Schön - Starker Regen/Schnee (langsamer)
of.options.RAIN.tooltip.5=  Aus - Kein Regen/Schnee (am schnellsten)
of.options.RAIN.tooltip.6=Wenn diese Einstellung deaktiviert ist, sind die Regen-
of.options.RAIN.tooltip.7=geräusche und -partikel dennoch zu hören und zu sehen.

of.options.SKY=Himmel
of.options.SKY.tooltip.1=Himmel
of.options.SKY.tooltip.2=  An - Himmel ist sichtbar (langsamer)
of.options.SKY.tooltip.3=  Aus - Himmel ist nicht sichtbar (schneller)
of.options.SKY.tooltip.4=Wenn dies deaktiviert ist, sind Mond und Sonne dennoch
of.options.SKY.tooltip.5=sichtbar.

of.options.STARS=Sterne
of.options.STARS.tooltip.1=Sterne
of.options.STARS.tooltip.2=  An - Sterne sind sichtbar (langsamer)
of.options.STARS.tooltip.3=  Aus - Sterne sind nicht sichtbar (schneller)

of.options.SUN_MOON=Sonne & Mond
of.options.SUN_MOON.tooltip.1=Sonne & Mond
of.options.SUN_MOON.tooltip.2=  An - Sonne und Mond sind sichtbar (Standard)
of.options.SUN_MOON.tooltip.3=  Aus - Sonne und Mond sind nicht sichtbar (schneller)

of.options.SHOW_CAPES=Umhänge
of.options.SHOW_CAPES.tooltip.1=Umhänge
of.options.SHOW_CAPES.tooltip.2=  An - Umhänge werden dargestellt (Standard)
of.options.SHOW_CAPES.tooltip.3=  Aus - Umhänge werden nicht dargestellt
of.options.SHOW_CAPES.tooltip.4=Diese Einstellung beeinflusst normalerweise nicht die
of.options.SHOW_CAPES.tooltip.5=Leistung.

of.options.TRANSLUCENT_BLOCKS=Blocktransparenz
of.options.TRANSLUCENT_BLOCKS.tooltip.1=Blocktransparenz
of.options.TRANSLUCENT_BLOCKS.tooltip.2=  Standard - Wie Grafikmodus
of.options.TRANSLUCENT_BLOCKS.tooltip.3=  Schön - Korrekte Farbmischung (langsamer)
of.options.TRANSLUCENT_BLOCKS.tooltip.4=  Schnell - Schnelle Farbmischung (schneller)
of.options.TRANSLUCENT_BLOCKS.tooltip.5=Kontrolliert die Farbmischung von transparenten Blöcken
of.options.TRANSLUCENT_BLOCKS.tooltip.6=mit verschiedenen Farben (Gefärbtes Glas, Wasser, Eis),
of.options.TRANSLUCENT_BLOCKS.tooltip.7=wenn sie hintereinander mit Luft dazwischen platziert
of.options.TRANSLUCENT_BLOCKS.tooltip.8=werden.

of.options.HELD_ITEM_TOOLTIPS=Gegenstandsbeschr.
of.options.HELD_ITEM_TOOLTIPS.tooltip.1=Gegenstandsbeschreibung
of.options.HELD_ITEM_TOOLTIPS.tooltip.2=  An - Zeige Gegenstandsbeschreibung (Standard)
of.options.HELD_ITEM_TOOLTIPS.tooltip.3=  Aus - Zeige keine Gegenstandsbeschreibung
of.options.HELD_ITEM_TOOLTIPS.tooltip.4=Wird über der Schnellzugriffsleiste angezeigt.

of.options.ADVANCED_TOOLTIPS=Erweiterte Schnellinfo
of.options.ADVANCED_TOOLTIPS.tooltip.1=Erweiterte Schnellinfo
of.options.ADVANCED_TOOLTIPS.tooltip.2=  An - Zeige erweiterte Schnellinfo
of.options.ADVANCED_TOOLTIPS.tooltip.3=  Aus - Keine erweiterte Schnellinfo (Standard)
of.options.ADVANCED_TOOLTIPS.tooltip.4=Zeigt ausführliche Informationen über Gegenstände
of.options.ADVANCED_TOOLTIPS.tooltip.5=(ID, Haltbarkeit) und Shader Einstellungen (ID, Quelle,
of.options.ADVANCED_TOOLTIPS.tooltip.6=Standardwert) an.

of.options.DROPPED_ITEMS=Gegenstände
of.options.DROPPED_ITEMS.tooltip.1=Liegende Gegenstände
of.options.DROPPED_ITEMS.tooltip.2=  Standard - Wie Grafikmodus
of.options.DROPPED_ITEMS.tooltip.3=  Schnell - Zweidimensionale Animation (schneller)
of.options.DROPPED_ITEMS.tooltip.4=  Schön - Dreidimensionale Animation (langsamer)

options.entityShadows.tooltip.1=Objektschatten
options.entityShadows.tooltip.2=  An - Zeige Objektschatten
options.entityShadows.tooltip.3=  Aus - Zeige keine Objektschatten

of.options.VIGNETTE=Vignette
of.options.VIGNETTE.tooltip.1=Visueller Effekt, der die Bildschirmecken abdunkelt
of.options.VIGNETTE.tooltip.2=  Standard - Wie Grafikmodus (Standard)
of.options.VIGNETTE.tooltip.3=  Schnell - Vignette deaktiviert (schneller)
of.options.VIGNETTE.tooltip.4=  Schön - Vignette aktiviert (langsamer)
of.options.VIGNETTE.tooltip.5=Die Vignette kann sich extrem auf die Leistung auswirken,
of.options.VIGNETTE.tooltip.6=besonders im Vollbildschirmmodus.
of.options.VIGNETTE.tooltip.7=Der Vignetteneffekt ist fast unmerklich und kann sicher
of.options.VIGNETTE.tooltip.8=deaktiviert werden.

of.options.DYNAMIC_FOV=Dynamisches Sichtfeld
of.options.DYNAMIC_FOV.tooltip.1=Dynamisches Sichtfeld
of.options.DYNAMIC_FOV.tooltip.2=  An - Dynamisches Sichtfeld aktivieren (Standard)
of.options.DYNAMIC_FOV.tooltip.3=  Aus - Dynamisches Sichtfeld ausschalten
of.options.DYNAMIC_FOV.tooltip.4=Ändert das Sichtfeld beim Fliegen, Sprinten, Spannen
of.options.DYNAMIC_FOV.tooltip.5=eines Bogens oder wenn man einen Schnelligkeits-Effekt hat.

of.options.DYNAMIC_LIGHTS=Dyn. Beleuchtung
of.options.DYNAMIC_LIGHTS.tooltip.1=Dynamische Beleuchtung
of.options.DYNAMIC_LIGHTS.tooltip.2=  Aus - Keine dynamische Beleuchtung (Standard)
of.options.DYNAMIC_LIGHTS.tooltip.3=  Schnell - Schnelle dyn. Beleuchtung (alle 500ms aktual.)
of.options.DYNAMIC_LIGHTS.tooltip.4=  Schön - Schöne dyn. Beleuchtung (Echtzeitaktual.)
of.options.DYNAMIC_LIGHTS.tooltip.5=Leuchtende Gegenstände (Fackel, Glowstone, etc.)
of.options.DYNAMIC_LIGHTS.tooltip.6=beleuchten die Umgebung, wenn sie in der Hand gehalten,
of.options.DYNAMIC_LIGHTS.tooltip.7=von anderen Spielern ausgerüstet oder gedroppt werden.

# Performance

of.options.SMOOTH_FPS=Stabile Bildrate
of.options.SMOOTH_FPS.tooltip.1=Stabilisiert Bildrate durch Grafiktreiberpuffer
of.options.SMOOTH_FPS.tooltip.2=  Aus - Keine Stabilisierung, Bildrate könnte schwanken
of.options.SMOOTH_FPS.tooltip.3=  An - Stabilisierung der Bildrate
of.options.SMOOTH_FPS.tooltip.4=Diese Einstellung ist abhängig vom Grafikkartentreiber.
of.options.SMOOTH_FPS.tooltip.5=Eine Wirkung ist nicht immer spürbar.

of.options.SMOOTH_WORLD=Weltstabilisierung
of.options.SMOOTH_WORLD.tooltip.1=Entfernt vom internen Server verursachte starke Lags
of.options.SMOOTH_WORLD.tooltip.2=  Aus - Keine Stabilisierung, Bildrate könnte schwanken
of.options.SMOOTH_WORLD.tooltip.3=  An - Weltstabilisierung aktiviert
of.options.SMOOTH_WORLD.tooltip.4=Stabilisiert die Bildrate, indem die interne Server-
of.options.SMOOTH_WORLD.tooltip.5=auslastung aufgeteilt wird.
of.options.SMOOTH_WORLD.tooltip=6=Funktioniert nur im Einzelspielermodus.

of.options.FAST_RENDER=Schnelles Rendern
of.options.FAST_RENDER.tooltip.1=Schnelles Rendern
of.options.FAST_RENDER.tooltip.2=  Aus - Standard-Rendern (Standard)
of.options.FAST_RENDER.tooltip.3=  An - Optimiertes Rendern (schneller)
of.options.FAST_RENDER.tooltip.4=Benutzt optimierte Renderalgorithmen, die die GPU-Aus-
of.options.FAST_RENDER.tooltip.5=lastung verringern und die Leistung erheblich steigern.
of.options.FAST_RENDER.tooltip.6=Dies kann zu Konflikten mit einigen Mods führen.

of.options.FAST_MATH=Schnelle Mathematik
of.options.FAST_MATH.tooltip.1=Schnelle Mathematik
of.options.FAST_MATH.tooltip.2=  Aus - Standard-Mathematik (Standard)
of.options.FAST_MATH.tooltip.3=  An - Schnellere Mathematik
of.options.FAST_MATH.tooltip.4=Benutzt optimierte Sinus- und Kosinusfunktionen, die den
of.options.FAST_MATH.tooltip.5=CPU-Zwischenspeicher besser nutzen und die Leistung
of.options.FAST_MATH.tooltip.6=steigern.
of.options.FAST_MATH.tooltip.7=Dies kann die Weltgeneration geringfügig beeinflussen.

of.options.CHUNK_UPDATES=Chunk-Aktualisierungen
of.options.CHUNK_UPDATES.tooltip.1=Chunk-Aktualisierungen
of.options.CHUNK_UPDATES.tooltip.2=  1 - Langsameres Weltladen, höhere Bildrate (Standard)
of.options.CHUNK_UPDATES.tooltip.3=  3 - Schnelleres Weltladen, niedrigere Bildrate
of.options.CHUNK_UPDATES.tooltip.4=  5 - Schnellstes Weltladen, niedrigste Bildrate
of.options.CHUNK_UPDATES.tooltip.5=Zahl an Chunk-Aktualisierungen pro gerendertem Bild.
of.options.CHUNK_UPDATES.tooltip.6=Höhere Werte könnten die Bildrate destabilisieren.

of.options.CHUNK_UPDATES_DYNAMIC=Dyn. Aktualisierungen
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.1=Dynamische Chunk-Aktualisierungen
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.2=  Aus - (Standard) Normale Chunk-Aktualisierungen
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.3=  An - Mehr Chunk-Aktualisierungen
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.4=Wenn diese Einstellung aktiviert ist, werden mehr Chunk-
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.5=Aktualisierungen ausgeführt, während der Spieler still
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.6=steht, um die Welt schneller zu laden.

of.options.LAZY_CHUNK_LOADING=Träges Chunkladen
of.options.LAZY_CHUNK_LOADING.tooltip.1=Träges Chunkladen
of.options.LAZY_CHUNK_LOADING.tooltip.2=  Aus - Standard-Server-Chunkladen
of.options.LAZY_CHUNK_LOADING.tooltip.3=  An - Träges Server-Chunkladen (flüssiger)
of.options.LAZY_CHUNK_LOADING.tooltip.4=Server-Chunkladen wird flüssiger, indem die Chunks über
of.options.LAZY_CHUNK_LOADING.tooltip.5=ein paar Ticks hinweg geladen werden. Deaktiviere dies,
of.options.LAZY_CHUNK_LOADING.tooltip.6=wenn Teile der Welt nicht korrekt geladen werden.
of.options.LAZY_CHUNK_LOADING.tooltip.7=Nur im Einzelspielermodus effektiv.

of.options.RENDER_REGIONS=Render-Regionen
of.options.RENDER_REGIONS.tooltip.1=Render-Regionen
of.options.RENDER_REGIONS.tooltip.2= Aus - (Standard) Render-Regionen deaktiviert
of.options.RENDER_REGIONS.tooltip.3= An - Render-Regionen aktiviert
of.options.RENDER_REGIONS.tooltip.4=Render-Regionen ermöglichen ein schnelleres Rendern
of.options.RENDER_REGIONS.tooltip.5=des Geländes bei großen Render-Distanzen.
of.options.RENDER_REGIONS.tooltip.6=Effektiver, wenn VBOs aktiviert sind.
of.options.RENDER_REGIONS.tooltip.6=Nicht für integrierte Grafikkarten empfohlen.

of.options.SMART_ANIMATIONS=Intelligente Animationen
of.options.SMART_ANIMATIONS.tooltip.1=Intelligente Animationen
of.options.SMART_ANIMATIONS.tooltip.2= Aus - Keine intelligenten Animationen (Standard)
of.options.SMART_ANIMATIONS.tooltip.3= An - Benutze intelligente Animationen
of.options.SMART_ANIMATIONS.tooltip.4=Es werden nur Texturen animiert, die gerade auf
of.options.SMART_ANIMATIONS.tooltip.5=dem Bildschirm sichtbar sind.
of.options.SMART_ANIMATIONS.tooltip.6=Dies reduziert Lag-Spitzen und erhöht die FPS.
of.options.SMART_ANIMATIONS.tooltip.7=Besonders nützlich bei großen Mod-Packs und
of.options.SMART_ANIMATIONS.tooltip.8=Ressourcenpaketen mit HD-Texturen.

# Animations

of.options.animation.allOn=Alle an
of.options.animation.allOff=Alle aus
of.options.animation.dynamic=Dynamisch

of.options.ANIMATED_WATER=Animiertes Wasser
of.options.ANIMATED_LAVA=Animierte Lava
of.options.ANIMATED_FIRE=Animiertes Feuer
of.options.ANIMATED_PORTAL=Animierte Portale
of.options.ANIMATED_REDSTONE=Animiertes Redstone
of.options.ANIMATED_EXPLOSION=Animierte Explosion
of.options.ANIMATED_FLAME=Animierte Flammen
of.options.ANIMATED_SMOKE=Animierter Rauch
of.options.VOID_PARTICLES=Leerepartikel
of.options.WATER_PARTICLES=Wasserpartikel
of.options.RAIN_SPLASH=Regengeplätscher
of.options.PORTAL_PARTICLES=Portalpartikel
of.options.POTION_PARTICLES=Trankpartikel
of.options.DRIPPING_WATER_LAVA=Wasser- & Lavatropfen
of.options.ANIMATED_TERRAIN=Animiertes Gelände
of.options.ANIMATED_TEXTURES=Animierte Texturen
of.options.FIREWORK_PARTICLES=Feuerwerkspartikel

# Other

of.options.LAGOMETER=Lagometer
of.options.LAGOMETER.tooltip.1=Zeigt das Lagometer auf dem Debugbildschirm (F3).
of.options.LAGOMETER.tooltip.2=* Orange - Speichermüllsammlung
of.options.LAGOMETER.tooltip.3=* Cyan - Tick
of.options.LAGOMETER.tooltip.4=* Blau - Geplante Ausführungen
of.options.LAGOMETER.tooltip.5=* Lila - Chunks hochladen
of.options.LAGOMETER.tooltip.6=* Rot - Chunkaktualisierungen
of.options.LAGOMETER.tooltip.7=* Gelb - Sichtbarkeitstest
of.options.LAGOMETER.tooltip.8=* Grün - Gelände rendern

of.options.PROFILER=Debug-Diagramm
of.options.PROFILER.tooltip.1=Debug-Diagramm
of.options.PROFILER.tooltip.2=  An - Debug-Diagramm ist aktiviert, langsamer
of.options.PROFILER.tooltip.3=  Aus - Debug-Diagramm ist nicht aktiviert, schneller
of.options.PROFILER.tooltip.4=Das Debug-Diagramm sammelt und stellt Debuginforma-
of.options.PROFILER.tooltip.5=tionen dar, wenn der Debugbildschirm geöffnet ist (F3).

of.options.WEATHER=Wetter
of.options.WEATHER.tooltip.1=Wetter
of.options.WEATHER.tooltip.2=  An - Wetter ist aktiv, langsamer
of.options.WEATHER.tooltip.3=  Aus - Wetter ist nicht aktiv, schneller
of.options.WEATHER.tooltip.4=Das Wetter kontrolliert Regen, Schnee und Gewitter.
of.options.WEATHER.tooltip.5=Wetterkontrolle ist nur im Einzelspielermodus möglich.

of.options.time.dayOnly=Nur Tag
of.options.time.nightOnly=Nur Nacht

of.options.TIME=Zeit
of.options.TIME.tooltip.1=Zeit
of.options.TIME.tooltip.2= Standard - Normaler Tag-Nacht-Zyklus
of.options.TIME.tooltip.3= Nur Tag - Nur Tag
of.options.TIME.tooltip.4= Nur Nacht - Nur Nacht
of.options.TIME.tooltip.5=Diese Einstellung ist nur im Kreativmodus und
of.options.TIME.tooltip.6=im Einzelspielermodus wirksam.

options.fullscreen.tooltip.1=Vollbildschirm
options.fullscreen.tooltip.2=  An - Benutze Vollbildschirmmodus
options.fullscreen.tooltip.3=  Aus - Benutze Fenstermodus
options.fullscreen.tooltip.4=Der Vollbildschirmmodus könnte schneller oder langsamer
options.fullscreen.tooltip.5=als der Fenstermodus sein, das kommt auf die Grafik-
options.fullscreen.tooltip.6=karte an.

of.options.FULLSCREEN_MODE=Vollbild-Auflösung
of.options.FULLSCREEN_MODE.tooltip.1=Vollbild-Auflösung
of.options.FULLSCREEN_MODE.tooltip.2=  Standard - Benutze Bildschirmauflösung, langsamer
of.options.FULLSCREEN_MODE.tooltip.3=  BxH - Benutze andere Auflösung, könnte schneller sein
of.options.FULLSCREEN_MODE.tooltip.4=Die ausgewählte Auflösung wird im Vollbildschirmmodus
of.options.FULLSCREEN_MODE.tooltip.5=verwendet (F11). Kleinere Auflösungen sollten generell
of.options.FULLSCREEN_MODE.tooltip.6=schneller sein.

of.options.SHOW_FPS=Bildrate anzeigen
of.options.SHOW_FPS.tooltip.1=Zeige kurze Bildrate- und Render-Informationen
of.options.SHOW_FPS.tooltip.2=  Fps - durchschnittlich/minimum
of.options.SHOW_FPS.tooltip.3=  C: - Chunkrenderer
of.options.SHOW_FPS.tooltip.4=  E: - Objektrenderer + Blockrenderer
of.options.SHOW_FPS.tooltip.5=  U: - Chunk-Aktualisierungen
of.options.SHOW_FPS.tooltip.6=Die Bildrate-Informationen werden nur gezeigt, wenn der
of.options.SHOW_FPS.tooltip.7=Debugbildschirm (F3) nicht sichtbar ist.

of.options.save.45s=45s
of.options.save.90s=90s
of.options.save.3min=3min
of.options.save.6min=6min
of.options.save.12min=12min
of.options.save.24min=24min

of.options.AUTOSAVE_TICKS=Autospeichern
of.options.AUTOSAVE_TICKS.tooltip.1=Autospeicherintervall
of.options.AUTOSAVE_TICKS.tooltip.2= 45s - Standard
of.options.AUTOSAVE_TICKS.tooltip.3=Autospeichern kann abhängig von der Render Distanz zu
of.options.AUTOSAVE_TICKS.tooltip.4=Lag-Spitzen führen.
of.options.AUTOSAVE_TICKS.tooltip.5=Die Welt wird auch gespeichert, wenn das Menü geöffnet ist.

of.options.SCREENSHOT_SIZE=Screenshot-Größe
of.options.SCREENSHOT_SIZE.tooltip.1=Screenshot-Auflösung
of.options.SCREENSHOT_SIZE.tooltip.2=  Standard - Standard-Screenshot-Auflösung
of.options.SCREENSHOT_SIZE.tooltip.3=  2x-4x - Benutzerdefinierte Screenshot-Auflösung
of.options.SCREENSHOT_SIZE.tooltip.4=Das Aufnehmen größerer Screenshots kann mehr
of.options.SCREENSHOT_SIZE.tooltip.5=Speicher benötigen.
of.options.SCREENSHOT_SIZE.tooltip.6=Nicht kompatibel mit Schnellem Rendern und Antialiasing.
of.options.SCREENSHOT_SIZE.tooltip.7=Erfordert Grafiktreiberpuffer-Unterstützung.

of.options.SHOW_GL_ERRORS=Zeige OpenGL Fehler
of.options.SHOW_GL_ERRORS.tooltip.1=Zeige OpenGL Fehler
of.options.SHOW_GL_ERRORS.tooltip.2=OpenGL Fehler werden im Chat angezeigt.
of.options.SHOW_GL_ERRORS.tooltip.3=Deaktiviere dies nur, wenn ein bekannter Fehler
of.options.SHOW_GL_ERRORS.tooltip.4=vorliegt und dieser nicht behoben werden kann.
of.options.SHOW_GL_ERRORS.tooltip.5=Auch nach Deaktivierung werden Fehler in der Log Datei
of.options.SHOW_GL_ERRORS.tooltip.6=festgehalten und es kann zu erheblichen FPS Einbrüchen
of.options.SHOW_GL_ERRORS.tooltip.7=kommen.

# Chat Settings

of.options.CHAT_BACKGROUND=Chat Hintergrund
of.options.CHAT_BACKGROUND.tooltip.1=Chat Hintergrund
of.options.CHAT_BACKGROUND.tooltip.2=  Standard - Feste Breite
of.options.CHAT_BACKGROUND.tooltip.3=  Kompakt - Entspricht Zeilenbreite
of.options.CHAT_BACKGROUND.tooltip.4=  Aus - Versteckt

of.options.CHAT_SHADOW=Chat Schatten
of.options.CHAT_SHADOW.tooltip.1=Chat Schatten
of.options.CHAT_SHADOW.tooltip.2=  An - Zeige Textschatten
of.options.CHAT_SHADOW.tooltip.3=  Aus - Kein Textschatten
