#Traduit en français québécois par Z0ul0u25

# General
of.general.ambiguous=Ambigue
of.general.compact=Compacte
of.general.custom=Personnalisé
of.general.from=De
of.general.id=ID
of.general.max=Maximum
of.general.restart=Redémarer
of.general.smart=Intelligent

# Keys 
of.key.zoom=Zoom

# Message
of.message.aa.shaders1=L'Anticrénelage n'est pas compatible avec les Shaders.
of.message.aa.shaders2=Veuillez désactivez les Shaders pour activer cette option.

of.message.af.shaders1=Le Filtrage Anisotropique n'est pas compatible avec les Shaders.
of.message.af.shaders2=Veuillez désactivez les Shaders pour activer cette option.

of.message.fr.shaders1=Le Rendu Rapide n'est pas compatible avec les Shaders.
of.message.fr.shaders2=Veuillez désactivez les Shaders pour activer cette option.

of.message.an.shaders1=L'Anaglyphe 3D n'est pas compatible avec les Shaders.
of.message.an.shaders2=Veuillez désactivez les Shaders pour activer cette option.

of.message.shaders.aa1=Les Shaders ne sont pas compatible avec l'Anticrénelage.
of.message.shaders.aa2=Veuillez fixé Qualité -> Anticrénelage à OFF puis redémarrer le jeu.

of.message.shaders.af1=Les Shaders ne sont pas compatible avec le Filtrage Anisotropique.
of.message.shaders.af2=Veuillez fixé Qualité -> Filtrage Anisotropique à OFF.

of.message.shaders.fr1=Les Shaders ne sont pas compatible avec le Rendu Rapide.
of.message.shaders.fr2=Veuillez fixé Performance -> Rendu Rapide à OFF.

of.message.shaders.an1=Les Shaders ne sont pas compatible avec l'Anaglyphe 3D.
of.message.shaders.an2=Veuillez configurer Autre -> Anaglyphe 3D à OFF.

of.message.shaders.nv1=Ce pack de Shaders a besoin d'une version d'Optifine plus récente: %s
of.message.shaders.nv2=Voulez-vous continuer pour vrai?

of.message.newVersion=Une nouvelle version d'§eOptiFine§f est disponible: §e%s§f
of.message.java64Bit=Pour de meilleures performances, installez une version §e64-bit de Java§f
of.message.openglError=§eErreur OpenGL§f: %s (%s)

of.message.shaders.loading=Chargement des Shaders: %s

of.message.other.reset=Réinitialiser tous les réglages vidéos à leurs valeurs de base?

of.message.loadingVisibleChunks=Chargement des Chunks visibles

# Skin customization

of.options.skinCustomisation.ofCape=Cape Optifine...

of.options.capeOF.title=Cape OptiFine
of.options.capeOF.openEditor=Ouvrir l'éditeur de Cape
of.options.capeOF.reloadCape=Actualiser la Cape
of.options.capeOF.copyEditorLink=Copier le lien

of.message.capeOF.openEditor=L'éditeur de Cape Optifine devrait s'ouvir dans un navigateur web.
of.message.capeOF.openEditorError=Une erreur c'est produite en ouvrant le lien dans un navigateur web.
of.message.capeOF.reloadCape=La Cape va s'actualiser dans 15 secondes.

of.message.capeOF.error1=Échec de l'authentification Mojang.
of.message.capeOF.error2=Error: %s

# Video settings

options.graphics.tooltip.1=Qualité visuel
options.graphics.tooltip.2=  Rapide - Basse qualité (Rapide)
options.graphics.tooltip.3=  Détaillée - Haute qualité (Lent)
options.graphics.tooltip.4=Change l'apparence des nuages, des feuilles,
options.graphics.tooltip.5=de l'eau, des ombres et du côté de l'herbe.

of.options.renderDistance.tiny=Mini
of.options.renderDistance.short=Courte
of.options.renderDistance.normal=Normale
of.options.renderDistance.far=Loin
of.options.renderDistance.extreme=Extrême
of.options.renderDistance.insane=Démentielle
of.options.renderDistance.ludicrous=Ridicule

options.renderDistance.tooltip.1=Distance de Rendu
options.renderDistance.tooltip.2=  2 Mini - 32m (Très rapide)
options.renderDistance.tooltip.3=  8 Normal - 128m (Normal)
options.renderDistance.tooltip.4=  16 Loin - 256m (Lent)
options.renderDistance.tooltip.5=  32 Extrême - 256m (Très lent)
options.renderDistance.tooltip.6=  48 Démentielle - 768m (Besoin de 2Go de RAM allouée)
options.renderDistance.tooltip.7=  64 Ridicule - 1024m (Besoin de 3Go de RAM alloué)
options.renderDistance.tooltip.8=Les valeurs au-dessus de 16 n'ont effet qu'en solo.

options.ao.tooltip.1=Luminosité adoucie
options.ao.tooltip.2=  OFF - aucun adoucissement (rapide)
options.ao.tooltip.3=  Minimum - Adoucissement simple (Lent)
options.ao.tooltip.4=  Maximum - Adoucissement complexe (Très lent)

options.framerateLimit.tooltip.1=FPS Max
options.framerateLimit.tooltip.2=  VSync - limite du moniteur (144, 60, 30, 20)
options.framerateLimit.tooltip.3=  5-255 - variable
options.framerateLimit.tooltip.4=  Illimitée - sans limite (Très rapide)
options.framerateLimit.tooltip.5=La limite de FPS diminue le taux d'image par seconde
options.framerateLimit.tooltip.6=même si la valeur limite n'est pas atteinte.
of.options.framerateLimit.vsync=VSync

of.options.AO_LEVEL=Ombres adoucies
of.options.AO_LEVEL.tooltip.1=Ombres adoucies
of.options.AO_LEVEL.tooltip.2=  OFF - aucune ombre
of.options.AO_LEVEL.tooltip.3=  50%% - légères ombres
of.options.AO_LEVEL.tooltip.4=  100%% - fortes ombres

options.viewBobbing.tooltip.1=Mouvements plus réalistes.
options.viewBobbing.tooltip.2=Lors de l'utilisation du mipmaps, configurer à OFF
options.viewBobbing.tooltip.3=pour de meilleurs résultats.

options.guiScale.tooltip.1=Taille du GUI
options.guiScale.tooltip.2=  Auto - taille maximale
options.guiScale.tooltip.3=  Petite, Normale, Grande - 1x à 3x
options.guiScale.tooltip.4=  4x à 10x - Disponible pour moniteur 4K
options.guiScale.tooltip.5=Les valeurs impaires (1x, 3x, 5x ...) sont incompatible avec l'Unicode.
options.guiScale.tooltip.6=Un GUI petit serai plus rapide

options.vbo=Utiliser les VBOs
options.vbo.tooltip.1=Objets de Mémoire Tampon
options.vbo.tooltip.2=Utilises des modèles de rendu qui sont habituellement
options.vbo.tooltip.3=plus rapides (5-10%%) que le rendu par de base.

options.gamma.tooltip.1=Change la luminosité des endroits sombres
options.gamma.tooltip.2=  Sombre - luminosité standard
options.gamma.tooltip.3=  1-99%% - variable
options.gamma.tooltip.4=  Lumineux - luminosité maximale pour les objets sombres
options.gamma.tooltip.5=Cette option ne change pas la luminosité des
options.gamma.tooltip.6=objets complètement noirs

options.anaglyph.tooltip.1=Anaglyphe 3D 
options.anaglyph.tooltip.2=Active un effet stéréoscopique 3D utilisant
options.anaglyph.tooltip.3=différentes couleurs pour chaque œil.
options.anaglyph.tooltip.4=Nécessite des lunettes rouge-cyan pour une vue adéquate.

options.attackIndicator.tooltip.1=Configure la position de l'indicateur d'attaque
options.attackIndicator.tooltip.2=  Viseur - Sous le viseur
options.attackIndicator.tooltip.3=  Barre - À coté de la barre d'items
options.attackIndicator.tooltip.4=  OFF - Aucun indicateur d'attaque
options.attackIndicator.tooltip.5=L'indicateur d'attaque montre la force
options.attackIndicator.tooltip.6=d'attaque de l'item tenu en main

of.options.ALTERNATE_BLOCKS=Blocs Alternatifs
of.options.ALTERNATE_BLOCKS.tooltip.1=Blocs Alternatifs
of.options.ALTERNATE_BLOCKS.tooltip.2=Utilises des modèles alternatifs pour certains blocs.
of.options.ALTERNATE_BLOCKS.tooltip.3=Dépend du pack de ressources sélectionné.

of.options.FOG_FANCY=Brouillard
of.options.FOG_FANCY.tooltip.1=Type de Brouillard
of.options.FOG_FANCY.tooltip.2=  Rapide - brouillard rapide
of.options.FOG_FANCY.tooltip.3=  Détaillé - meilleure apparence (Plus lent)
of.options.FOG_FANCY.tooltip.4=  OFF - Aucun Brouillard (très rapide)
of.options.FOG_FANCY.tooltip.5=Le brouillard détaillé n'est disponible que
of.options.FOG_FANCY.tooltip.6=si la carte graphique peut le supporter.

of.options.FOG_START=Commencement du Brouillard
of.options.FOG_START.tooltip.1=Commencement du Brouillard
of.options.FOG_START.tooltip.2=  0.2 - Commence proche du joueur
of.options.FOG_START.tooltip.3=  0.8 - Commence loin du joueur
of.options.FOG_START.tooltip.4=Cette option n'affecte généralement pas les performances.

of.options.CHUNK_LOADING=Chargement des Chunks
of.options.CHUNK_LOADING.tooltip.1=Chargement des Chunks
of.options.CHUNK_LOADING.tooltip.2=  Défaut - FPS instable lors du chargement des chunks
of.options.CHUNK_LOADING.tooltip.3=  Adoucie - FPS stable
of.options.CHUNK_LOADING.tooltip.4=  Multi-Coeur - FPS stable, Chargement 3x plus rapide
of.options.CHUNK_LOADING.tooltip.5=Adoucie et Multi-Coeur évite les freezes et
of.options.CHUNK_LOADING.tooltip.6=les lags causés par le chargement des chunks.
of.options.CHUNK_LOADING.tooltip.7=Multi-Coeur peut augmenter de 3x la vitesse de chargement
of.options.CHUNK_LOADING.tooltip.8=et augmenter les FPS en utilisant un deuxième Coeur du CPU.
of.options.chunkLoading.smooth=Adoucie
of.options.chunkLoading.multiCore=Multi-Coeur

of.options.shaders=Shaders...
of.options.shadersTitle=Shaders

of.options.shaders.packNone=OFF
of.options.shaders.packDefault=(interne)

of.options.shaders.ANTIALIASING=Anticrénelage
of.options.shaders.ANTIALIASING.tooltip.1=Anticrénelage
of.options.shaders.ANTIALIASING.tooltip.2=  OFF - (de base) sans anticrénelage (rapide)
of.options.shaders.ANTIALIASING.tooltip.3=  FXAA 2x, 4x - Lignes et rebords lissé (lent)
of.options.shaders.ANTIALIASING.tooltip.4=FXAA est un effet de post-traitement qui adoucit les
of.options.shaders.ANTIALIASING.tooltip.5=lignes rugueuses et transitions rapides de couleur.
of.options.shaders.ANTIALIASING.tooltip.6=Il est plus rapide que l'anticrénelage traditionnel
of.options.shaders.ANTIALIASING.tooltip.7=et est compatible avec les shaders et le rendu rapide. 

of.options.shaders.NORMAL_MAP=Map Normale
of.options.shaders.NORMAL_MAP.tooltip.1=Normal Map
of.options.shaders.NORMAL_MAP.tooltip.2=  ON - (de base) Active les cartes normales 
of.options.shaders.NORMAL_MAP.tooltip.3=  OFF - Désavtive les cartes normales
of.options.shaders.NORMAL_MAP.tooltip.4=Les cartes normales peuvent être utilisées par le pack de
of.options.shaders.NORMAL_MAP.tooltip.5=shader pour simuler la géométrie 3D sur des surfaces de modèles
of.options.shaders.NORMAL_MAP.tooltip.6=plats. Les textures de cartes normales sont fournies par
of.options.shaders.NORMAL_MAP.tooltip.7=le pack de ressources utilisé.

of.options.shaders.SPECULAR_MAP=Carte spéculaire 
of.options.shaders.SPECULAR_MAP.tooltip.1=Carte spéculaire
of.options.shaders.SPECULAR_MAP.tooltip.2=  ON - (de base) Active les cartes spéculaires
of.options.shaders.SPECULAR_MAP.tooltip.3=  OFF - Désactive les cartes spéculaires
of.options.shaders.SPECULAR_MAP.tooltip.4=Les cartes spéculaires peuvent être utilisées par le pack de
of.options.shaders.SPECULAR_MAP.tooltip.5=shader pour simuler des reflets de lumière sur les surfaces.
of.options.shaders.SPECULAR_MAP.tooltip.6=Les textures de cartes spéculaires sont fournies par
of.options.shaders.SPECULAR_MAP.tooltip.7=le pack de ressources utilisé.

of.options.shaders.RENDER_RES_MUL=Qualité du rendu 
of.options.shaders.RENDER_RES_MUL.tooltip.1=Qualité du rendu
of.options.shaders.RENDER_RES_MUL.tooltip.2=  0.5x - Faible (rapide)
of.options.shaders.RENDER_RES_MUL.tooltip.3=  1x - Standard (de base)
of.options.shaders.RENDER_RES_MUL.tooltip.4=  2x - Haute (lent)
of.options.shaders.RENDER_RES_MUL.tooltip.5=La qualité du rendu contrôle la taille des textures
of.options.shaders.RENDER_RES_MUL.tooltip.6=dont les shaders vont faire le rendu.
of.options.shaders.RENDER_RES_MUL.tooltip.7=Des valeurs faibles peuvent être utiles pour les écrans 4K.
of.options.shaders.RENDER_RES_MUL.tooltip.8=Des valeurs hautes servent de filtre d'anticrénelage.

of.options.shaders.SHADOW_RES_MUL=Qualité des ombres 
of.options.shaders.SHADOW_RES_MUL.tooltip.1=Qualité des ombres
of.options.shaders.SHADOW_RES_MUL.tooltip.2=  0.5x - faible (rapide)
of.options.shaders.SHADOW_RES_MUL.tooltip.3=  1x - standard (de base)
of.options.shaders.SHADOW_RES_MUL.tooltip.4=  2x - haute (lent)
of.options.shaders.SHADOW_RES_MUL.tooltip.5=La qualité des ombres contrôle la taille de la texture
of.options.shaders.SHADOW_RES_MUL.tooltip.6=de la map des ombres utilisée par les shaders.
of.options.shaders.SHADOW_RES_MUL.tooltip.7=Valeurs faibles = ombres grossières, inexactes.
of.options.shaders.SHADOW_RES_MUL.tooltip.8=Valeurs hautes = ombres subtiles, détaillées.

of.options.shaders.HAND_DEPTH_MUL=Profondeur de main 
of.options.shaders.HAND_DEPTH_MUL.tooltip.1=Profondeur de main
of.options.shaders.HAND_DEPTH_MUL.tooltip.2=  0.5x - Main proche de la caméra
of.options.shaders.HAND_DEPTH_MUL.tooltip.3=  1x - (de base)
of.options.shaders.HAND_DEPTH_MUL.tooltip.4=  2x - Main éloignée de la caméra
of.options.shaders.HAND_DEPTH_MUL.tooltip.5=La profondeur de main contrôle l'éloignement des
of.options.shaders.HAND_DEPTH_MUL.tooltip.6=objets tenus, par rapport à la caméra.
of.options.shaders.HAND_DEPTH_MUL.tooltip.7=Ça devrait changer le flou des objets en main
of.options.shaders.HAND_DEPTH_MUL.tooltip.8=pour les shaders avec un flou de profondeur de champ.

of.options.shaders.CLOUD_SHADOW=Ombre des nuages

of.options.shaders.OLD_HAND_LIGHT=Ancien éclairage de main 
of.options.shaders.OLD_HAND_LIGHT.tooltip.1=Ancien éclairage de main
of.options.shaders.OLD_HAND_LIGHT.tooltip.2=  De base - Géré par le shader
of.options.shaders.OLD_HAND_LIGHT.tooltip.3=  Oui - Utilise l'ancien éclairage de main
of.options.shaders.OLD_HAND_LIGHT.tooltip.4=  Non - Utilise le nouvel éclairage de main
of.options.shaders.OLD_HAND_LIGHT.tooltip.5=Ce réglage permet aux shaders qui ne reconnaissent que
of.options.shaders.OLD_HAND_LIGHT.tooltip.6=les objets lumineux depuis la main principale
of.options.shaders.OLD_HAND_LIGHT.tooltip.7=de marcher avec les objets dans la seconde main.

of.options.shaders.OLD_LIGHTING=Ancien éclairage 
of.options.shaders.OLD_LIGHTING.tooltip.1=Ancien éclairage
of.options.shaders.OLD_LIGHTING.tooltip.2=  De base - Géré par le shader
of.options.shaders.OLD_LIGHTING.tooltip.3=  Oui - Utilise l'ancien éclairage
of.options.shaders.OLD_LIGHTING.tooltip.4=  Non - N'utilise pas l'ancien éclairage
of.options.shaders.OLD_LIGHTING.tooltip.5=L'ancien éclairage contrôle l'éclairage fixe imposé
of.options.shaders.OLD_LIGHTING.tooltip.6=de base aux côtés des blocs.
of.options.shaders.OLD_LIGHTING.tooltip.7=Les shaders avec des ombres apportent généralement
of.options.shaders.OLD_LIGHTING.tooltip.8=un éclairage bien meilleur, selon la position du soleil.

of.options.shaders.DOWNLOAD=Télécharger les shaders 
of.options.shaders.DOWNLOAD.tooltip.1=Télécharger les shaders
of.options.shaders.DOWNLOAD.tooltip.2= 
of.options.shaders.DOWNLOAD.tooltip.3=Ouvre la page des packs de shaders dans un navigateur web.
of.options.shaders.DOWNLOAD.tooltip.4=Mettez les packs téléchargés dans le "Dossier des shaders"
of.options.shaders.DOWNLOAD.tooltip.5=et ils apparaîtront dans la liste des shaders installés.

of.options.shaders.SHADER_PACK=Pack de shaders

of.options.shaders.shadersFolder=Dossier des Shaders
of.options.shaders.shaderOptions=Options du Shader...

of.options.shaderOptionsTitle=Options du Shader

of.options.quality=Qualité...
of.options.qualityTitle=Paramètre de la Qualité

of.options.details=Détails...
of.options.detailsTitle=Paramètre des Détails

of.options.performance=Performance...
of.options.performanceTitle=Paramètre des Performances

of.options.animations=Animations...
of.options.animationsTitle=Paramètre des Animations

of.options.other=Autre...
of.options.otherTitle=Autre Paramètre

of.options.other.reset=Réinitialiser les réglages vidéos...

of.shaders.profile=Profil

# Quality

of.options.mipmap.bilinear=Bilinéaire
of.options.mipmap.linear=Linéaire
of.options.mipmap.nearest=Rapproché
of.options.mipmap.trilinear=Trilinéaire

options.mipmapLevels.tooltip.1=Effet visuelle qui rend les objets lointains plus beaux
options.mipmapLevels.tooltip.2=en adoucissant les détails
options.mipmapLevels.tooltip.3=  OFF - Aucun adoucissement
options.mipmapLevels.tooltip.4=  1 - Adoucissement minimum
options.mipmapLevels.tooltip.5=  4 - Adoucissement maximum
options.mipmapLevels.tooltip.6=Cette option n'affecte généralement pas les performances.

of.options.MIPMAP_TYPE=Type de Mipmap
of.options.MIPMAP_TYPE.tooltip.1=Effet visuel qui rend les objets lointains plus beaux
of.options.MIPMAP_TYPE.tooltip.2=en adoucissant les détails
of.options.MIPMAP_TYPE.tooltip.3=  Rapproché - adoucissement esquissé (rapide)
of.options.MIPMAP_TYPE.tooltip.4=  Linéaire - adoucissement normal
of.options.MIPMAP_TYPE.tooltip.5=  Bilinéaire - adoucissement travaillé
of.options.MIPMAP_TYPE.tooltip.6=  Trilinéaire - adoucissement magnifique (lent)

of.options.AA_LEVEL=Anticrénelage
of.options.AA_LEVEL.tooltip.1=Anticrénelage
of.options.AA_LEVEL.tooltip.2= OFF - (de base) sans anticrénelage (rapide)
of.options.AA_LEVEL.tooltip.3= 2-16 - Anticrénelage des lignes et bordures (lent)
of.options.AA_LEVEL.tooltip.4=L'Anticrénelage adouci l'effet rugueux et
of.options.AA_LEVEL.tooltip.5=les transitions rapides de couleurs.
of.options.AA_LEVEL.tooltip.6=Lorsque activer, les FPS peuvent ralentir.
of.options.AA_LEVEL.tooltip.7=Tous les niveaux ne sont pas supportés par toutes les cartes graphiques.
of.options.AA_LEVEL.tooltip.8=N'a d'effets qu'après un REDÉMARRAGE!

of.options.AF_LEVEL=Filtrage Anisotropique
of.options.AF_LEVEL.tooltip.1=Filtrage Anisotropique
of.options.AF_LEVEL.tooltip.2= OFF - (de base) détail de texture standard (rapide)
of.options.AF_LEVEL.tooltip.3= 2-16 - Détails plus fins dans les textures mipmappées (lent)
of.options.AF_LEVEL.tooltip.4=Le Filtrage Anisotropique rétablie les détails dans
of.options.AF_LEVEL.tooltip.5=les textures mipmappées.
of.options.AF_LEVEL.tooltip.6=Lorsque activé, les FPS peuvent ralentir.

of.options.CLEAR_WATER=Eau Claire
of.options.CLEAR_WATER.tooltip.1=Eau Claire
of.options.CLEAR_WATER.tooltip.2=  ON - Eau transparente, éclaircie
of.options.CLEAR_WATER.tooltip.3=  OFF - Eau de base

of.options.RANDOM_ENTITIES=Entités Aléatoires
of.options.RANDOM_ENTITIES.tooltip.1=Entités Aléatoires
of.options.RANDOM_ENTITIES.tooltip.2=  OFF - Aucune entité aléatoire (rapide)
of.options.RANDOM_ENTITIES.tooltip.3=  ON - Avec entité aléatoire (lent)
of.options.RANDOM_ENTITIES.tooltip.4=Les entités utilisent des textures aléatoires
of.options.RANDOM_ENTITIES.tooltip.5=À besoin d'un pack de ressources avec 
of.options.RANDOM_ENTITIES.tooltip.6=plusieurs textures d'une même entité.

of.options.BETTER_GRASS=Meilleur Gazon
of.options.BETTER_GRASS.tooltip.1=Meilleur Gazon
of.options.BETTER_GRASS.tooltip.2=  OFF - texture de base (rapide)
of.options.BETTER_GRASS.tooltip.3=  Rapide - bord de gazon complet (lent)
of.options.BETTER_GRASS.tooltip.4=  Détaillé - bord de gazon détaillé (très lent)

of.options.BETTER_SNOW=Meilleur Neige
of.options.BETTER_SNOW.tooltip.1=Meilleur Neige
of.options.BETTER_SNOW.tooltip.2=  OFF - Neige de base (rapide)
of.options.BETTER_SNOW.tooltip.3=  ON - Meilleure Neige (lent)
of.options.BETTER_SNOW.tooltip.4=Apparait en dessous des blocs transparents (clôture, haute herbe)
of.options.BETTER_SNOW.tooltip.5=lorsque qu'entouré de neige.

of.options.CUSTOM_FONTS=Police Personnalisée
of.options.CUSTOM_FONTS.tooltip.1=Police Personnalisée
of.options.CUSTOM_FONTS.tooltip.2=  ON - utilise la police personnalisée (de base, lent)
of.options.CUSTOM_FONTS.tooltip.3=  OFF - utilise la police de base (rapide)
of.options.CUSTOM_FONTS.tooltip.4=La Police Personnalisée est fournis par
of.options.CUSTOM_FONTS.tooltip.5=le pack de ressources utilisé.

of.options.CUSTOM_COLORS=Couleurs Personnalisées
of.options.CUSTOM_COLORS.tooltip.1=Couleurs Personnalisées
of.options.CUSTOM_COLORS.tooltip.2=  ON - utilise des couleurs personnalisées (de base, lent)
of.options.CUSTOM_COLORS.tooltip.3=  OFF - utilise les couleurs de base (rapide)
of.options.CUSTOM_COLORS.tooltip.4=Les Couleurs Personnalisées sont fournis par
of.options.CUSTOM_COLORS.tooltip.5=le pack de ressources utilisé.

of.options.SWAMP_COLORS=Couleurs Marécageuses
of.options.SWAMP_COLORS.tooltip.1=Couleurs Marécageuses
of.options.SWAMP_COLORS.tooltip.2=  ON - utilise les couleurs marécageuses (de base, lent)
of.options.SWAMP_COLORS.tooltip.3=  OFF - n'utilise pas les couleurs marécageuses (rapide)
of.options.SWAMP_COLORS.tooltip.4=La couleur marécageuse modifie le gazon, les feuilles,
of.options.SWAMP_COLORS.tooltip.5=les lianes et l'eau.

of.options.SMOOTH_BIOMES=Biomes Adoucis
of.options.SMOOTH_BIOMES.tooltip.1=Biomes Adoucis
of.options.SMOOTH_BIOMES.tooltip.2=  ON - adoucie la bordure des biomes (de base, lent)
of.options.SMOOTH_BIOMES.tooltip.3=  OFF - aucun adoucissement de bordure des biomes (rapide)
of.options.SMOOTH_BIOMES.tooltip.4=L'adoucissement des bordures de biomes est fait en échantillonnant et
of.options.SMOOTH_BIOMES.tooltip.5=moyennant la couleur des blocs aux frontières entre deux biomes.
of.options.SMOOTH_BIOMES.tooltip.6=N'affecte que le gazon, les feuilles, les lianes et l'eau.

of.options.CONNECTED_TEXTURES=Textures Connectées
of.options.CONNECTED_TEXTURES.tooltip.1=Textures Connectées
of.options.CONNECTED_TEXTURES.tooltip.2=  OFF - aucune connexion (de base)
of.options.CONNECTED_TEXTURES.tooltip.3=  Rapide - connexion rapide
of.options.CONNECTED_TEXTURES.tooltip.4=  Fancy - connexion détaillée
of.options.CONNECTED_TEXTURES.tooltip.5=Les Textures Connectées connecte les textures de
of.options.CONNECTED_TEXTURES.tooltip.6=la vitre, le grès et les bibliothèques placés une
of.options.CONNECTED_TEXTURES.tooltip.7=à coté de l'autre. Les Textures Connectées sont
of.options.CONNECTED_TEXTURES.tooltip.8=fournis par le pack de ressources utilisé.

of.options.NATURAL_TEXTURES=Textures Naturelles
of.options.NATURAL_TEXTURES.tooltip.1=Textures Naturelles
of.options.NATURAL_TEXTURES.tooltip.2=  OFF - sans textures naturelles (de base)
of.options.NATURAL_TEXTURES.tooltip.3=  ON - avec textures naturelles
of.options.NATURAL_TEXTURES.tooltip.4=Les textures naturelles enlèvent le pattern grillagé
of.options.NATURAL_TEXTURES.tooltip.5=créé par la répétition de bloc identique.
of.options.NATURAL_TEXTURES.tooltip.6=L'utilisation de textures retournées et renversées
of.options.NATURAL_TEXTURES.tooltip.7=est utilisé. La configuration pour les textures naturelles
of.options.NATURAL_TEXTURES.tooltip.8=sont fournies par le pack de ressources utilisé.

of.options.CUSTOM_SKY=Ciel Personnalisé
of.options.CUSTOM_SKY.tooltip.1=Ciel Personnalisé
of.options.CUSTOM_SKY.tooltip.2=  ON - Texture personnalisée (de base, lent)
of.options.CUSTOM_SKY.tooltip.3=  OFF - Ciel de base (rapide)
of.options.CUSTOM_SKY.tooltip.4=La texture du ciel personnalisé est
of.options.CUSTOM_SKY.tooltip.5=fourni par le pack de ressources utilisé.

of.options.CUSTOM_ITEMS=Items Personnalisés
of.options.CUSTOM_ITEMS.tooltip.1=Items Personnalisés
of.options.CUSTOM_ITEMS.tooltip.2=  ON - Textures d'items personnalisés (de base, lent)
of.options.CUSTOM_ITEMS.tooltip.3=  OFF - Textures d'items de base (rapide)
of.options.CUSTOM_ITEMS.tooltip.4=Les textures d'items personnalisés sont
of.options.CUSTOM_ITEMS.tooltip.5=fournis par le pack de ressources utilisé.

of.options.CUSTOM_ENTITY_MODELS=Modèles d'Entités Personnalisées
of.options.CUSTOM_ENTITY_MODELS.tooltip.1=Modèles d'Entités Personnalisées
of.options.CUSTOM_ENTITY_MODELS.tooltip.2=  ON - Modèles d'entités personnalisées (de base, lent)
of.options.CUSTOM_ENTITY_MODELS.tooltip.3=  OFF - Modèles d'entités de base (rapide)
of.options.CUSTOM_ENTITY_MODELS.tooltip.4=Les modèles d'entités personnalisées sont
of.options.CUSTOM_ENTITY_MODELS.tooltip.5=fournis par le pack de ressources utilisé.

# Details

of.options.CLOUDS=Nuages
of.options.CLOUDS.tooltip.1=Nuages
of.options.CLOUDS.tooltip.2=  Défaut - comme dans les configurations Graphiques
of.options.CLOUDS.tooltip.3=  Rapides - basse qualité (rapde)
of.options.CLOUDS.tooltip.4=  Détaillés - haute qualité (lent)
of.options.CLOUDS.tooltip.5=  OFF - sans nuages (plus rapide)
of.options.CLOUDS.tooltip.6=Les nuages rapides sont rendus en 2D.
of.options.CLOUDS.tooltip.7=Les nuages détaillés sont rendus en 3D.

of.options.CLOUD_HEIGHT=Hauteur des Nuages
of.options.CLOUD_HEIGHT.tooltip.1=Hauteur des Nuages
of.options.CLOUD_HEIGHT.tooltip.2=  OFF - hauteur de base
of.options.CLOUD_HEIGHT.tooltip.3=  100%% - au-dessus de la hauteur limite du monde

of.options.TREES=Arbres
of.options.TREES.tooltip.1=Arbres
of.options.TREES.tooltip.2=  Défaut - comme dans les configurations Graphiques
of.options.TREES.tooltip.3=  Rapides - basse qualité (plus rapide)
of.options.TREES.tooltip.4=  Intelligents - haute qualité (rapide)
of.options.TREES.tooltip.5=  Détaillés - très haute qualité (lent)
of.options.TREES.tooltip.6=Les arbres rapides ont des feuilles opaques.
of.options.TREES.tooltip.7=Les arbres intelligents et détaillés
of.options.TREES.tooltip.8=ont des feuilles transparentes.

of.options.RAIN=Pluie & Neige
of.options.RAIN.tooltip.1=Pluie & Neige
of.options.RAIN.tooltip.2=  Défaut - Comme dans les configurations Graphiques
of.options.RAIN.tooltip.3=  Rapide  - Légère pluie/neige (rapide)
of.options.RAIN.tooltip.4=  Détaillé - Grosse pluie/neige (lent)
of.options.RAIN.tooltip.5=  OFF - Sans pluie/neige (plus rapide)
of.options.RAIN.tooltip.6=Même si la pluie & la neige est à OFF,
of.options.RAIN.tooltip.7=les éclaboussures et le son sont toujours actifs.

of.options.SKY=Ciel
of.options.SKY.tooltip.1=Ciel
of.options.SKY.tooltip.2=  ON - Le ciel est visible (lent)
of.options.SKY.tooltip.3=  OFF  - Le ciel n'est pas visible (rapide)
of.options.SKY.tooltip.4=Quand le ciel est à OFF,
of.options.SKY.tooltip.5=le soleil et la lune sont toujours visibles.

of.options.STARS=Étoiles
of.options.STARS.tooltip.1=Étoiles
of.options.STARS.tooltip.2=  ON - Les étoiles sont visible (lent)
of.options.STARS.tooltip.3=  OFF  - Les étoiles ne sont pas visibles (rapide)

of.options.SUN_MOON=Soleil & lune
of.options.SUN_MOON.tooltip.1=Soleil & lune
of.options.SUN_MOON.tooltip.2=  ON - Le soleil et la lune sont visibles (de base)
of.options.SUN_MOON.tooltip.3=  OFF  - Le soleil et la lune ne sont pas visibles (rapide)

of.options.SHOW_CAPES=Capes Visibles
of.options.SHOW_CAPES.tooltip.1=Capes visibles
of.options.SHOW_CAPES.tooltip.2=  ON - Afficher la cape de joueurs (de base)
of.options.SHOW_CAPES.tooltip.3=  OFF - Ne pas afficher la cape des joueurs

of.options.TRANSLUCENT_BLOCKS=Blocs Translucides
of.options.TRANSLUCENT_BLOCKS.tooltip.1=Blocs Translucides
of.options.TRANSLUCENT_BLOCKS.tooltip.2=  Détaillés - Mélange correct des couleurs (de base)
of.options.TRANSLUCENT_BLOCKS.tooltip.3=  Rapides - Mélange rapide ds couleurs (rapide)
of.options.TRANSLUCENT_BLOCKS.tooltip.4=Contrôle le mélange des couleurs des blocs translucides
of.options.TRANSLUCENT_BLOCKS.tooltip.5=avec différentes couleurs (verre teindu, eau, glace)
of.options.TRANSLUCENT_BLOCKS.tooltip.6=lorsque placé un à côté de l'autre avec de l'air entre eux.

of.options.HELD_ITEM_TOOLTIPS=Info-bulles d'Item
of.options.HELD_ITEM_TOOLTIPS.tooltip.1=Info-bulles d'Item
of.options.HELD_ITEM_TOOLTIPS.tooltip.2=  ON - affiche des infos sur l'item en main (de base)
of.options.HELD_ITEM_TOOLTIPS.tooltip.3=  OFF - n'affiche pas d'info sur l'item en main

of.options.ADVANCED_TOOLTIPS=Info-bulles Avancé
of.options.ADVANCED_TOOLTIPS.tooltip.1=Info-bulles Avancé
of.options.ADVANCED_TOOLTIPS.tooltip.2=  ON - Affiche les infobulles avancées
of.options.ADVANCED_TOOLTIPS.tooltip.3=  OFF - N'affiche pas les infobulles avancées (de base)
of.options.ADVANCED_TOOLTIPS.tooltip.4=Les infobulles avancées affiche des informations détaillées
of.options.ADVANCED_TOOLTIPS.tooltip.5=sur l'item (id, durabilité) et les options des shaders
of.options.ADVANCED_TOOLTIPS.tooltip.6=(id, source, valeurs de base).

of.options.DROPPED_ITEMS=Items Droppés
of.options.DROPPED_ITEMS.tooltip.1=Dropped Items
of.options.DROPPED_ITEMS.tooltip.2=  Défaut - Comme dans les configurations Graphiques
of.options.DROPPED_ITEMS.tooltip.3=  Rapides - Items 2D (rapide)
of.options.DROPPED_ITEMS.tooltip.4=  Détaillés - Items 3D (lent)

options.entityShadows.tooltip.1=Ombres des Entités
options.entityShadows.tooltip.2=  ON - affiche l'ombre des entités
options.entityShadows.tooltip.3=  OFF - n'affiche pas l'ombre des entités

of.options.VIGNETTE=Vignette
of.options.VIGNETTE.tooltip.1=Effet visuel qui assombrie les coins de l'écran
of.options.VIGNETTE.tooltip.2=  Défaut - comme dans les configurations Graphiques
of.options.VIGNETTE.tooltip.3=  Rapide - vignette désactivée (rapide)
of.options.VIGNETTE.tooltip.4=  Détaillée - vignette activée (lent)
of.options.VIGNETTE.tooltip.5=La vignette à un effet significatif sur les FPS,
of.options.VIGNETTE.tooltip.6=surtout lorsque le jeu est en plein écran.
of.options.VIGNETTE.tooltip.7=L'effet de vignette est très subtile et
of.options.VIGNETTE.tooltip.8=peut être désactivé en toute sécurité.

of.options.DYNAMIC_FOV=CV Dynamique
of.options.DYNAMIC_FOV.tooltip.1=CV Dynamique
of.options.DYNAMIC_FOV.tooltip.2=  ON - Activer le CV dynamique (de base)
of.options.DYNAMIC_FOV.tooltip.3=  OFF - Désactiver le CV dynamique
of.options.DYNAMIC_FOV.tooltip.4=Changer le champ visuel (CV) lors du vol, du sprint
of.options.DYNAMIC_FOV.tooltip.5=de la nage et du bandage d'un arc.

of.options.DYNAMIC_LIGHTS=Lumières Dynamiques
of.options.DYNAMIC_LIGHTS.tooltip.1=Lumières Dynamiques
of.options.DYNAMIC_LIGHTS.tooltip.2=  OFF - aucune lumière dynamique (de base)
of.options.DYNAMIC_LIGHTS.tooltip.3=  Rapides - lumières dynamiques rapides (rafraichi à tous les 500ms)
of.options.DYNAMIC_LIGHTS.tooltip.4=  Détaillées - Lumières dynamiques détaillées (rafraichi en temps réel)
of.options.DYNAMIC_LIGHTS.tooltip.5=Active la lumière des items (torche, pierre lumineuse, etc.)
of.options.DYNAMIC_LIGHTS.tooltip.6=pour illuminer tout autour quand l'item et en main,
of.options.DYNAMIC_LIGHTS.tooltip.7=equipper par d'autres joueurs ou jeter au sol.

options.biomeBlendRadius.tooltip.1=Adoucit la transition de couleur entre les biomes
options.biomeBlendRadius.tooltip.2=  OFF - Pas de transition (rapide)
options.biomeBlendRadius.tooltip.3=  5x5 - Transition normal (de base)
options.biomeBlendRadius.tooltip.4=  15x15 - Transition maximale (lent)
options.biomeBlendRadius.tooltip.5=De grandes valeurs peuvent générer des pics de lag
options.biomeBlendRadius.tooltip.6=et ralentir le chargement des chunks.

# Performance

of.options.SMOOTH_FPS=FPS Atténué
of.options.SMOOTH_FPS.tooltip.1=Stabilise les FPS en supprimant les tampons de mémoires graphiques.
of.options.SMOOTH_FPS.tooltip.2=  OFF - Aucune stabilisation, Les FPS peuvent varier
of.options.SMOOTH_FPS.tooltip.3=  ON - Stabilisation des FPS
of.options.SMOOTH_FPS.tooltip.4=Cette option dépend du gestionnaire graphique, les
of.options.SMOOTH_FPS.tooltip.5=effets ne sont pas toujours visibles.

of.options.SMOOTH_WORLD=Monde Atténué
of.options.SMOOTH_WORLD.tooltip.1=Élimine les piques de lag causés par le serveur interne.
of.options.SMOOTH_WORLD.tooltip.2=  OFF - Aucune stabilisation, Les FPS peuvent varier
of.options.SMOOTH_WORLD.tooltip.3=  ON - Stabilisation des FPS
of.options.SMOOTH_WORLD.tooltip.4=Stabilise les FPS en distribuant les charges du serveur interne.
of.options.SMOOTH_WORLD.tooltip.5=Fonctionne seulement en local (monde solo).

of.options.FAST_RENDER=Rendu Rapide
of.options.FAST_RENDER.tooltip.1=Rendu Rapide
of.options.FAST_RENDER.tooltip.2= OFF - Rendu standard (de base)
of.options.FAST_RENDER.tooltip.3= ON - Rendu optimisé (rapide)
of.options.FAST_RENDER.tooltip.4=Utilise des algorithmes de rendu optimisé pour diminuer
of.options.FAST_RENDER.tooltip.5=la charge du GPU et peut augmenter les FPS.

of.options.FAST_MATH=Maths Rapides
of.options.FAST_MATH.tooltip.1=Maths Rapides
of.options.FAST_MATH.tooltip.2= OFF - Math standard (de base)
of.options.FAST_MATH.tooltip.3= ON - Math optimisé
of.options.FAST_MATH.tooltip.4=Utilise des fonctions optimisées de sin() et cos() qui peuvent
of.options.FAST_MATH.tooltip.5=mieux utiliser la cache du CPU et augmenter les FPS.

of.options.CHUNK_UPDATES=Mise à Jour des Chunks
of.options.CHUNK_UPDATES.tooltip.1=Mise à Jour des Chunks
of.options.CHUNK_UPDATES.tooltip.2= 1 - Chargement lent du monde, meilleur FPS (de base)
of.options.CHUNK_UPDATES.tooltip.3= 3 - Chargement rapide du monde, bas FPS
of.options.CHUNK_UPDATES.tooltip.4= 5 - Chargement très rapide du monde, très bas FPS
of.options.CHUNK_UPDATES.tooltip.5=Nombre de Mise à jour par image de rendu (tick),
of.options.CHUNK_UPDATES.tooltip.6=Les valeurs élevées peut déstabilisé les FPS.

of.options.CHUNK_UPDATES_DYNAMIC=Mises à Jour Dynamiques
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.1=Mises à Jours Dynamiques
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.2= OFF - Mise à jour standard (de base)
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.3= ON - Plus de mise à jour lorsque le joueur ne bouge pas
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.4=Les mises à jour dynamiques force plus de
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.5=chunks à ce mettre à jour quand le joueur
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.6=est inactif pour charger le monde plus vite.

of.options.LAZY_CHUNK_LOADING=Chargement des Chunks Lâches
of.options.LAZY_CHUNK_LOADING.tooltip.1=Chargement Lâche des Chunks
of.options.LAZY_CHUNK_LOADING.tooltip.2= OFF - Chargement des chunks serveurs de base
of.options.LAZY_CHUNK_LOADING.tooltip.3= ON - Chargement des chunks serveur lâche (plus doux)
of.options.LAZY_CHUNK_LOADING.tooltip.4=Adoucie le chargement des chunks serveurs intégrés
of.options.LAZY_CHUNK_LOADING.tooltip.5=en distribuant les chunks sur plusieurs ticks.
of.options.LAZY_CHUNK_LOADING.tooltip.6=Tourné à OFF si des parties du monde ne se charge pas correctement.
of.options.LAZY_CHUNK_LOADING.tooltip.7=Fonctionne seulement en local (monde solo) et les CPU à un coeur.

of.options.RENDER_REGIONS=Rendu des régions 
of.options.RENDER_REGIONS.tooltip.1=Rendu des régions
of.options.RENDER_REGIONS.tooltip.2= OFF - Rendu standard (de base)
of.options.RENDER_REGIONS.tooltip.3= ON - Rendu des régions
of.options.RENDER_REGIONS.tooltip.4=Permet un rendu du terrain plus rapide à des distances
of.options.RENDER_REGIONS.tooltip.5=d'affichage plus élevées. Plus efficace lorsque les VBOs
of.options.RENDER_REGIONS.tooltip.6=sont activés. Non recommandé pour les cartes graphiques
of.options.RENDER_REGIONS.tooltip.7=intégrées.

of.options.SMART_ANIMATIONS=Animations intelligentes 
of.options.SMART_ANIMATIONS.tooltip.1=Animations intelligentes
of.options.SMART_ANIMATIONS.tooltip.2= OFF - Animations standards (de base)
of.options.SMART_ANIMATIONS.tooltip.3= ON - Animations intelligentes
of.options.SMART_ANIMATIONS.tooltip.4=Permet au jeu d'animer uniquement les textures
of.options.SMART_ANIMATIONS.tooltip.5=actuellement visibles à l'écran. Ça réduit les pics
of.options.SMART_ANIMATIONS.tooltip.6=de lag et augmente les FPS. Utile surtout pour les gros
of.options.SMART_ANIMATIONS.tooltip.7=packs de mods et les packs de ressources HD.

# Animations

of.options.animation.allOn=Tous à ON
of.options.animation.allOff=Tous à OFF
of.options.animation.dynamic=Dynamique

of.options.ANIMATED_WATER=Eau Animée
of.options.ANIMATED_LAVA=Lave Animée
of.options.ANIMATED_FIRE=Feux Animé
of.options.ANIMATED_PORTAL=Portail Animé
of.options.ANIMATED_REDSTONE=Redstone Animée
of.options.ANIMATED_EXPLOSION=Explosion Animée
of.options.ANIMATED_FLAME=Flamme Animée
of.options.ANIMATED_SMOKE=Fumée Animée
of.options.VOID_PARTICLES=Particules de Vide
of.options.WATER_PARTICLES=Particules d'Eau
of.options.RAIN_SPLASH=Particules de Pluie
of.options.PORTAL_PARTICLES=Particules de Portail
of.options.POTION_PARTICLES=Particules de Potion
of.options.DRIPPING_WATER_LAVA=Goutelettes d'Eau/de Lave
of.options.ANIMATED_TERRAIN=Terrain Animé
of.options.ANIMATED_TEXTURES=Textures Animées
of.options.FIREWORK_PARTICLES=Particules de Feux d'Artifice

# Other

of.options.LAGOMETER=Lagomèter
of.options.LAGOMETER.tooltip.1=Affiche le Lagomètre sur l'écran de débogage (F3).
of.options.LAGOMETER.tooltip.2=* Orange - Récupérateur de mémoire
of.options.LAGOMETER.tooltip.3=* Cyan - Tick
of.options.LAGOMETER.tooltip.4=* Blue - Executables programmés
of.options.LAGOMETER.tooltip.5=* Purple - Chunks téléchargés
of.options.LAGOMETER.tooltip.6=* Red - Mises à jour de chunk
of.options.LAGOMETER.tooltip.7=* Yellow - Contrôle de visibilité
of.options.LAGOMETER.tooltip.8=* Green - Terrain rendu

of.options.PROFILER=Profileur de Débogage
of.options.PROFILER.tooltip.1=Profileur de Débogage
of.options.PROFILER.tooltip.2=  ON - Profileur actif (lent)
of.options.PROFILER.tooltip.3=  OFF - Profileur non actif (rapide)
of.options.PROFILER.tooltip.4=Le profileur de débogage collecte et affiche des informations de
of.options.PROFILER.tooltip.5=débogage lorsque l'écran de débogage (F3) est ouvert.

of.options.WEATHER=Météo
of.options.WEATHER.tooltip.1=Météo
of.options.WEATHER.tooltip.2=  ON - Météo active (lent)
of.options.WEATHER.tooltip.3=  OFF - Météo non active (rapide)
of.options.WEATHER.tooltip.4=La météo contrôle la pluie, la neige et l'orage.
of.options.WEATHER.tooltip.5=Le contrôle de la météo n'est possible qu'en local.

of.options.time.dayOnly=Jour Seulement
of.options.time.nightOnly=Nuit Seulement

of.options.TIME=temps
of.options.TIME.tooltip.1=Temps
of.options.TIME.tooltip.2= Défaut - Cycle jour/nuit normal
of.options.TIME.tooltip.3= Jour Seulement - Jour seulement
of.options.TIME.tooltip.4= Nuit Seulement - Nuit seulement
of.options.TIME.tooltip.5=La séléction du temps n'est possible qu'en CRÉATIF
of.options.TIME.tooltip.6=dans un monde local.

options.fullscreen.tooltip.1=Plein Écran
options.fullscreen.tooltip.2=  ON - Jeu en plein écran
options.fullscreen.tooltip.3=  OFF - Jeu fenêtré
options.fullscreen.tooltip.4=Le mode plein écran peut être plus rapide ou plus lent
options.fullscreen.tooltip.5=que le mode fenêtré, ça dépend de la carte graphique.

of.options.FULLSCREEN_MODE=Option Plein Écran
of.options.FULLSCREEN_MODE.tooltip.1=Option Plein Écran
of.options.FULLSCREEN_MODE.tooltip.2=  Défaut - Utilise la résolution du moniteur (lent)
of.options.FULLSCREEN_MODE.tooltip.3=  LxH - Utilise une résolution personnalisée (rapide)
of.options.FULLSCREEN_MODE.tooltip.4=La résolution sélectionnée est utilisé en mode plein écran (F11).
of.options.FULLSCREEN_MODE.tooltip.5=Une résolution plus basse devrait être plus rapide.

of.options.SHOW_FPS=Afficher les FPS
of.options.SHOW_FPS.tooltip.1=Affiche les FPS et informations de rendu.
of.options.SHOW_FPS.tooltip.2=  C: - Chunk rendus
of.options.SHOW_FPS.tooltip.3=  E: - Entités/bloc entités rendus
of.options.SHOW_FPS.tooltip.4=  U: - Mises à jour de chunk
of.options.SHOW_FPS.tooltip.5=Les informations sont présenté de manière compacte dans le coin
of.options.SHOW_FPS.tooltip.6=supérieur gauche lorsque l'écran de débogage n'est pas visible

of.options.save.45s=45s
of.options.save.90s=90s
of.options.save.3min=3min
of.options.save.6min=6min
of.options.save.12min=12min
of.options.save.24min=24min

of.options.AUTOSAVE_TICKS=Sauvegarde Automatique
of.options.AUTOSAVE_TICKS.tooltip.1=Intervale entre les sauvegardes
of.options.AUTOSAVE_TICKS.tooltip.2=L'intervalle de base (2s) n'est PAS RECOMMENDER.
of.options.AUTOSAVE_TICKS.tooltip.3=Les sauvegardes automatiques sont la cause
of.options.AUTOSAVE_TICKS.tooltip.4=du Pique de Lag de la Mort qui Tue.

of.options.SCREENSHOT_SIZE=Taille des Captures d'Écrans
of.options.SCREENSHOT_SIZE.tooltip.1=Taille des Captures d'Écrans
of.options.SCREENSHOT_SIZE.tooltip.2=  Défaut - Taille de la fenêtre
of.options.SCREENSHOT_SIZE.tooltip.3=  2x-4x - Taille personnalisée
of.options.SCREENSHOT_SIZE.tooltip.4=Prendre une capture d'écran plus grande utilise plus de mémoire.
of.options.SCREENSHOT_SIZE.tooltip.5=Incompatible avec le Rendu Rapide et l'Anticrénelage.
of.options.SCREENSHOT_SIZE.tooltip.6=Nécessite un GPU avec un tampon d'image.

of.options.SHOW_GL_ERRORS=Afficher les erreurs GL 
of.options.SHOW_GL_ERRORS.tooltip.1=Afficher les erreurs OpenGL
of.options.SHOW_GL_ERRORS.tooltip.2=Les erreurs OpenGL sont affichées dans le tchat lorsque
of.options.SHOW_GL_ERRORS.tooltip.3=cette option est activée. Ne la désactiver que s'il y a un
of.options.SHOW_GL_ERRORS.tooltip.4=conflit inconnu et que les erreurs ne peuvent être
of.options.SHOW_GL_ERRORS.tooltip.5=corrigées. Les erreurs sont toujours journalisées lorsque
of.options.SHOW_GL_ERRORS.tooltip.6=cette option est désactivée et peuvent toujours causer
of.options.SHOW_GL_ERRORS.tooltip.7=une chute importante de FPS.

# Chat Settings

of.options.CHAT_BACKGROUND=Fond du tchat 
of.options.CHAT_BACKGROUND.tooltip.1=Fond du tchat
of.options.CHAT_BACKGROUND.tooltip.2=  Par défaut - Largeur fixe
of.options.CHAT_BACKGROUND.tooltip.3=  Compact - S'adapte à la largeur des lignes
of.options.CHAT_BACKGROUND.tooltip.4=  OFF - Masqué

of.options.CHAT_SHADOW=Ombre du tchat 
of.options.CHAT_SHADOW.tooltip.1=Ombre du tchat
of.options.CHAT_SHADOW.tooltip.2=  ON - Ombre du tchat (de base)
of.options.CHAT_SHADOW.tooltip.3=  OFF - Pas d'ombre du tchat
