# Contributors of Chinese localization #
#   HeartyYF From InfinityStudio 2016-2-18 ---- 2020-7-25
#   xwjcool123 From Rainbow Pixel 2018-5-20 ---- 2018-6-16
#   xuyu_staryG(gxy17886) From InfinityStudio 2016-1-18 ---- 2016-1-19
#   hukk From MCBBS 2013-7-13 ---- 2016-11-9
#   shengjing1 From MCBBS 2012-11-20 ---- 2013-4-20

#   一些约定：英文或数字前后以空格区分，除非与全角标点直接相连；中文间标点用全角，数字或英文间标点用半角+空格；
#   凡是某个选项后的“较快”“较慢”“默认”一律以括号标记，不论原语言文件是括号还是逗号；若默认和较快较慢之类并列，将默认放于选项首，否则放于选项尾。
#   这些约定不必须遵守，我只是认为这样能增强可读性。具体示例参见下文。

# General
of.general.ambiguous=模糊
of.general.compact=紧凑
of.general.custom=自定义
of.general.from=来自
of.general.id=ID
of.general.max=最大化
of.general.restart=重启游戏
of.general.smart=智能

# Keys
of.key.zoom=缩放

# Message
of.message.aa.shaders1=抗锯齿与光影不兼容。
of.message.aa.shaders2=请关闭光影以启用此选项。

of.message.af.shaders1=各向异性过滤与光影不兼容。
of.message.af.shaders2=请关闭光影以启用此选项。

of.message.fr.shaders1=快速渲染与光影不兼容。
of.message.fr.shaders2=请关闭光影以启用此选项。

of.message.an.shaders1=3D 效果与光影不兼容。
of.message.an.shaders2=请关闭光影以启用此选项。

of.message.shaders.aa1=光影与抗锯齿不兼容。
of.message.shaders.aa2=请将 品质 -> 抗锯齿 选项设置为 关闭 并重启您的游戏。

of.message.shaders.af1=光影与各向异性过滤不兼容。
of.message.shaders.af2=请将 品质 -> 各向异性过滤 选项设置为 关闭。

of.message.shaders.fr1=光影与快速渲染不兼容。
of.message.shaders.fr2=请将 性能 -> 快速渲染 选项设置为 关闭。

of.message.shaders.gf1=光影与极佳的图像品质不兼容。
of.message.shaders.gf2=请将图像品质设置为流畅或高品质。

of.message.shaders.an1=光影与 3D 效果不兼容。
of.message.shaders.an2=请将 其他 -> 3D 效果 选项设置为 关闭。

of.message.shaders.nv1=此光影需要较新的OptiFine版本：%s
of.message.shaders.nv2=无论如何都要继续吗？

of.message.newVersion=新的§eOptiFine§f版本现已可用：§e%s§f
of.message.java64Bit=您可以安装§e64位Java§f来提升性能。
of.message.openglError=§eOpenGL错误§f：%s（%s）

of.message.shaders.loading=加载光影：%s

of.message.other.reset=确定要重置所有视频设置为默认值吗？

of.message.loadingVisibleChunks=正在加载可见区块

# Skin customization

of.options.skinCustomisation.ofCape=OptiFine 披风...

of.options.capeOF.title=OptiFine 披风
of.options.capeOF.openEditor=打开披风编辑器
of.options.capeOF.reloadCape=重新加载披风
of.options.capeOF.copyEditorLink=复制链接至剪贴板

of.message.capeOF.openEditor=OptiFine 披风编辑器会在网页浏览器中打开。
of.message.capeOF.openEditorError=在网页浏览器中打开编辑器时出现错误。
of.message.capeOF.reloadCape=披风将在 15 秒内重新加载。

of.message.capeOF.error1=Mojang 身份验证失败。
of.message.capeOF.error2=错误：%s

# Video settings

options.graphics.tooltip.1=图像品质
options.graphics.tooltip.2=  流畅   - 低品质（较快）
options.graphics.tooltip.3=  高品质 - 高品质（较慢）
options.graphics.tooltip.4=  极佳   - 更好的半透明对象（最慢）
options.graphics.tooltip.5=改变云、树叶、水、
options.graphics.tooltip.6=阴影和草地的外观。
options.graphics.tooltip.7=极佳的图像品质与光影不兼容。

of.options.renderDistance.tiny=最近
of.options.renderDistance.short=近
of.options.renderDistance.normal=中等
of.options.renderDistance.far=远
of.options.renderDistance.extreme=极远
of.options.renderDistance.insane=疯狂
of.options.renderDistance.ludicrous=荒唐

options.renderDistance.tooltip.1=能见度
options.renderDistance.tooltip.2=  2 最近 - 32m（最快）
options.renderDistance.tooltip.3=  8 中等 - 128m（正常）
options.renderDistance.tooltip.4=  16 远 - 256m（较慢）
options.renderDistance.tooltip.5=  32 极远 - 512m（最慢！）非常消耗资源！
options.renderDistance.tooltip.6=  48 疯狂 - 768m，需要分配至少 2G 内存
options.renderDistance.tooltip.7=  64 荒唐 - 1024m，需要分配至少 3G 内存
options.renderDistance.tooltip.8=超过 16 的能见度值只在本地世界有效。

options.entityDistanceScaling.tooltip.1=实体渲染距离
options.entityDistanceScaling.tooltip.2=  50%% - 较快
options.entityDistanceScaling.tooltip.3=  100%% - 默认
options.entityDistanceScaling.tooltip.4=  500%% - 较慢
options.entityDistanceScaling.tooltip.5=修改显示实体的最远距离。

options.ao.tooltip.1=平滑光照
options.ao.tooltip.2=  关闭 - 禁用平滑光照（较快）
options.ao.tooltip.3=  最小 - 简单流畅的平滑光照（较慢）
options.ao.tooltip.4=  最大 - 复杂柔和的平滑光照（最慢）

options.framerateLimit.tooltip.1=最大帧率
options.framerateLimit.tooltip.2=  垂直同步 - 限制为显示器帧率（60, 30, 20）
options.framerateLimit.tooltip.3=  5-255 - 可变
options.framerateLimit.tooltip.4=  无限制 - 无限制（最快）
options.framerateLimit.tooltip.5=帧率限制将降低 FPS，
options.framerateLimit.tooltip.6=即使其未达到上限值。
of.options.framerateLimit.vsync=垂直同步

of.options.AO_LEVEL=平滑光照级别
of.options.AO_LEVEL.tooltip.1=平滑光照级别
of.options.AO_LEVEL.tooltip.2=  关闭 - 不产生阴影
of.options.AO_LEVEL.tooltip.3=  50%% - 较浅的阴影
of.options.AO_LEVEL.tooltip.4=  100%% - 较深的阴影

options.viewBobbing.tooltip.1=更真实的运动。
options.viewBobbing.tooltip.2=当使用多级纹理贴图（mipmap）时，关闭此选项以获得最佳效果。

options.guiScale.tooltip.1=界面尺寸
options.guiScale.tooltip.2=  自动 - 最大尺寸
options.guiScale.tooltip.3=  小，中，大 - 1x 到 3x
options.guiScale.tooltip.4=  4x 到 10x - 在 4K 分辨率下可用
options.guiScale.tooltip.5=奇数值（1x, 3x, 5x之类）与 Unicode 不兼容。
options.guiScale.tooltip.6=较小的界面或许会更快。

options.vbo=启用顶点缓冲器
options.vbo.tooltip.1=顶点缓冲区对象
options.vbo.tooltip.2=使用一种替选的渲染模式，通常
options.vbo.tooltip.3=可以比默认渲染快（5-10%%）。

options.gamma.tooltip.1=增加较暗物体的亮度
options.gamma.tooltip.2=  标准 - 标准亮度
options.gamma.tooltip.3=  1-99%% - 可变亮度
options.gamma.tooltip.4=  明亮 - 最大亮度
options.gamma.tooltip.5=此选项不会改变
options.gamma.tooltip.6=完全黑色的物体的亮度。

options.anaglyph.tooltip.1=3D 效果
options.anaglyph.tooltip.2=通过为双眼分配不同颜色
options.anaglyph.tooltip.3=以实现 3D 立体效果。
options.anaglyph.tooltip.4=正常观看需要使用红蓝眼镜。

options.attackIndicator.tooltip.1=配置攻击指示器的位置
options.attackIndicator.tooltip.2=  十字准线 - 在十字准线下
options.attackIndicator.tooltip.3=  快捷栏 - 快捷栏旁
options.attackIndicator.tooltip.4=  关闭 - 无攻击指示器
options.attackIndicator.tooltip.5=攻击指示器显示
options.attackIndicator.tooltip.6=当前所持物品的攻击力。

of.options.ALTERNATE_BLOCKS=替选方块
of.options.ALTERNATE_BLOCKS.tooltip.1=替选方块
of.options.ALTERNATE_BLOCKS.tooltip.2=为一些方块使用备选的方块模型。
of.options.ALTERNATE_BLOCKS.tooltip.3=取决于所选的资源包。

of.options.FOG_FANCY=迷雾
of.options.FOG_FANCY.tooltip.1=迷雾类型
of.options.FOG_FANCY.tooltip.2=  流畅 - 较流畅的迷雾
of.options.FOG_FANCY.tooltip.3=  高品质 - 较慢的迷雾（质量更佳）
of.options.FOG_FANCY.tooltip.4=  关闭 - 无迷雾（最快）
of.options.FOG_FANCY.tooltip.5=高品质的迷雾
of.options.FOG_FANCY.tooltip.6=需要显卡支持才可用。

of.options.FOG_START=迷雾起始位置
of.options.FOG_START.tooltip.1=迷雾起始位置
of.options.FOG_START.tooltip.2=  0.2 - 迷雾起始于玩家周围
of.options.FOG_START.tooltip.3=  0.8 - 迷雾起始于离玩家较远的地方
of.options.FOG_START.tooltip.4=此选项通常不会影响性能。

of.options.CHUNK_LOADING=区块加载
of.options.CHUNK_LOADING.tooltip.1=区块加载
of.options.CHUNK_LOADING.tooltip.2=  默认 - 当加载区块时 FPS 不稳定
of.options.CHUNK_LOADING.tooltip.3=  平滑 - 稳定 FPS
of.options.CHUNK_LOADING.tooltip.4=  多核心 - 稳定 FPS ，3倍的世界加载速度
of.options.CHUNK_LOADING.tooltip.5=平滑和多核心可消除由区块
of.options.CHUNK_LOADING.tooltip.6=加载引起的延迟和卡顿。
of.options.CHUNK_LOADING.tooltip.7=多核心可以令世界加载速度提升3倍
of.options.CHUNK_LOADING.tooltip.8=并通过使用多个 CPU 核心来提升 FPS。
of.options.chunkLoading.smooth=平滑
of.options.chunkLoading.multiCore=多核心

of.options.shaders=光影...
of.options.shadersTitle=光影

of.options.shaders.packNone=关闭
of.options.shaders.packDefault=（内置）

of.options.shaders.ANTIALIASING=抗锯齿
of.options.shaders.ANTIALIASING.tooltip.1=抗锯齿
of.options.shaders.ANTIALIASING.tooltip.2=  关闭 - （默认）禁用抗锯齿（较快）
of.options.shaders.ANTIALIASING.tooltip.3=  FXAA 2x, 4x - 对线和边缘抗锯齿处理（较慢）
of.options.shaders.ANTIALIASING.tooltip.4=FXAA 是一种使锯齿状线条线和鲜明的色彩过渡
of.options.shaders.ANTIALIASING.tooltip.5=更为平滑的后处理特效。
of.options.shaders.ANTIALIASING.tooltip.6=它不仅快于传统的抗锯齿处理，
of.options.shaders.ANTIALIASING.tooltip.7=同时也与光影和快速渲染兼容。

of.options.shaders.NORMAL_MAP=法线贴图
of.options.shaders.NORMAL_MAP.tooltip.1=法线贴图
of.options.shaders.NORMAL_MAP.tooltip.2=  开启 - （默认）启用法线贴图
of.options.shaders.NORMAL_MAP.tooltip.3=  关闭 - 禁用法线贴图
of.options.shaders.NORMAL_MAP.tooltip.4=光影包可以使用法线贴图
of.options.shaders.NORMAL_MAP.tooltip.5=以在模型的平面表面内模拟三维几何结构。
of.options.shaders.NORMAL_MAP.tooltip.6=法线贴图材质一般由
of.options.shaders.NORMAL_MAP.tooltip.7=当前的资源包提供。

of.options.shaders.SPECULAR_MAP=高光贴图
of.options.shaders.SPECULAR_MAP.tooltip.1=高光贴图
of.options.shaders.SPECULAR_MAP.tooltip.2=  开启 - （默认）启用高光贴图
of.options.shaders.SPECULAR_MAP.tooltip.3=  关闭 - 禁用高光贴图
of.options.shaders.SPECULAR_MAP.tooltip.4=光影包可以使用高光贴图
of.options.shaders.SPECULAR_MAP.tooltip.5=以模拟特殊的反射效果。
of.options.shaders.SPECULAR_MAP.tooltip.6=高光贴图材质一般由
of.options.shaders.SPECULAR_MAP.tooltip.7=当前的资源包提供。

of.options.shaders.RENDER_RES_MUL=渲染精细度
of.options.shaders.RENDER_RES_MUL.tooltip.1=渲染精细度
of.options.shaders.RENDER_RES_MUL.tooltip.2=  0.5x - 低（最快）
of.options.shaders.RENDER_RES_MUL.tooltip.3=  1x - 标准（默认）
of.options.shaders.RENDER_RES_MUL.tooltip.4=  2x - 高（最慢）
of.options.shaders.RENDER_RES_MUL.tooltip.5=渲染精细度控制
of.options.shaders.RENDER_RES_MUL.tooltip.6=光影包当前渲染的材质大小。
of.options.shaders.RENDER_RES_MUL.tooltip.7=较低的值或许利于4K显示。
of.options.shaders.RENDER_RES_MUL.tooltip.8=较高的值将以抗锯齿滤镜的形式工作。

of.options.shaders.SHADOW_RES_MUL=阴影精细度
of.options.shaders.SHADOW_RES_MUL.tooltip.1=阴影精细度
of.options.shaders.SHADOW_RES_MUL.tooltip.2=  0.5x - 低（最快）
of.options.shaders.SHADOW_RES_MUL.tooltip.3=  1x - 标准（默认）
of.options.shaders.SHADOW_RES_MUL.tooltip.4=  2x - 高（最慢）
of.options.shaders.SHADOW_RES_MUL.tooltip.5=阴影精细度控制
of.options.shaders.SHADOW_RES_MUL.tooltip.6=光影包使用的阴影映射贴图大小。
of.options.shaders.SHADOW_RES_MUL.tooltip.7=较低的值 = 不精确的、粗糙的阴影。
of.options.shaders.SHADOW_RES_MUL.tooltip.8=较高的值 = 细致的、更佳的阴影。

of.options.shaders.HAND_DEPTH_MUL=手部景深
of.options.shaders.HAND_DEPTH_MUL.tooltip.1=手部景深
of.options.shaders.HAND_DEPTH_MUL.tooltip.2=  0.5x - 手与镜头相距较近
of.options.shaders.HAND_DEPTH_MUL.tooltip.3=  1x - （默认）
of.options.shaders.HAND_DEPTH_MUL.tooltip.4=  2x - 手与镜头相距较远
of.options.shaders.HAND_DEPTH_MUL.tooltip.5=手部景深控制
of.options.shaders.HAND_DEPTH_MUL.tooltip.6=手持的物品与镜头相距的距离。
of.options.shaders.HAND_DEPTH_MUL.tooltip.7=对于使用模糊景深的光影包，
of.options.shaders.HAND_DEPTH_MUL.tooltip.8=此选项应当会改变手持物品的模糊程度。

of.options.shaders.CLOUD_SHADOW=云朵阴影

of.options.shaders.OLD_HAND_LIGHT=经典手持光源
of.options.shaders.OLD_HAND_LIGHT.tooltip.1=经典手持光源
of.options.shaders.OLD_HAND_LIGHT.tooltip.2=  默认 - 光影包控制
of.options.shaders.OLD_HAND_LIGHT.tooltip.3=  开启 - 使用经典手持光源
of.options.shaders.OLD_HAND_LIGHT.tooltip.4=  关闭 - 使用新式手持光源
of.options.shaders.OLD_HAND_LIGHT.tooltip.5=经典手持光源允许只能辨别
of.options.shaders.OLD_HAND_LIGHT.tooltip.6=主手中发光物品的光影包
of.options.shaders.OLD_HAND_LIGHT.tooltip.7=对于另一只手也能工作。

of.options.shaders.OLD_LIGHTING=经典光效
of.options.shaders.OLD_LIGHTING.tooltip.1=经典光效
of.options.shaders.OLD_LIGHTING.tooltip.2=  默认 - 光影包控制
of.options.shaders.OLD_LIGHTING.tooltip.3=  开启 - 使用经典光效
of.options.shaders.OLD_LIGHTING.tooltip.4=  关闭 - 不使用经典光效
of.options.shaders.OLD_LIGHTING.tooltip.5=使用经典光效时则采用原版的固定照明。
of.options.shaders.OLD_LIGHTING.tooltip.6=在这种情况下，方块各面的照明会始终如一。
of.options.shaders.OLD_LIGHTING.tooltip.7=使用阴影的光影包通常会提供
of.options.shaders.OLD_LIGHTING.tooltip.8=更好的、取决于太阳位置的照明。

of.options.shaders.DOWNLOAD=光影下载
of.options.shaders.DOWNLOAD.tooltip.1=下载光影
of.options.shaders.DOWNLOAD.tooltip.2= 
of.options.shaders.DOWNLOAD.tooltip.3=在浏览器中打开光影包页面。
of.options.shaders.DOWNLOAD.tooltip.4=将已下载完成的光影置于光影文件夹中，
of.options.shaders.DOWNLOAD.tooltip.5=它们将出现在“已安装的光影”列表里。

of.options.shaders.SHADER_PACK=光影包文件夹

of.options.shaders.shadersFolder=光影包文件夹
of.options.shaders.shaderOptions=光影设置...

of.options.shaderOptionsTitle=光影设置

of.options.quality=品质...
of.options.qualityTitle=品质设置

of.options.details=细节...
of.options.detailsTitle=细节设置

of.options.performance=性能...
of.options.performanceTitle=性能设置

of.options.animations=动画...
of.options.animationsTitle=动画设置

of.options.other=其他...
of.options.otherTitle=其他设置

of.options.other.reset=重置视频设置...

of.shaders.profile=配置文件

# Quality

of.options.mipmap.bilinear=双线性
of.options.mipmap.linear=线性
of.options.mipmap.nearest=邻近
of.options.mipmap.trilinear=三线性

options.mipmapLevels.tooltip.1=通过平滑材质细节，使远处的
options.mipmapLevels.tooltip.2=物体获得更好的视觉效果。
options.mipmapLevels.tooltip.3=  关闭 - 无平滑
options.mipmapLevels.tooltip.4=  1 - 最小平滑
options.mipmapLevels.tooltip.5=  最大化 - 最大平滑
options.mipmapLevels.tooltip.6=此选项通常不会影响性能。

of.options.MIPMAP_TYPE=多级纹理（mipmap）类型
of.options.MIPMAP_TYPE.tooltip.1=通过平滑材质细节，使远处的
of.options.MIPMAP_TYPE.tooltip.2=物体获得更好的视觉效果。
of.options.MIPMAP_TYPE.tooltip.3=  邻近 - 粗略平滑（最快）
of.options.MIPMAP_TYPE.tooltip.4=  线性 - 正常平滑
of.options.MIPMAP_TYPE.tooltip.5=  双线性 - 精细平滑
of.options.MIPMAP_TYPE.tooltip.6=  三线性 - 极致平滑（最慢）

of.options.AA_LEVEL=抗锯齿
of.options.AA_LEVEL.tooltip.1=抗锯齿
of.options.AA_LEVEL.tooltip.2= 关闭 - （默认）禁用抗锯齿（较快）
of.options.AA_LEVEL.tooltip.3= 2-16 - 对线条和边缘进行抗锯齿处理（较慢）
of.options.AA_LEVEL.tooltip.4=抗锯齿对锯齿状的线条
of.options.AA_LEVEL.tooltip.5=和鲜明的色彩过渡进行平滑处理。
of.options.AA_LEVEL.tooltip.6=启用它可能会大幅降低 FPS。
of.options.AA_LEVEL.tooltip.7=并非所有级别都被显卡支持。
of.options.AA_LEVEL.tooltip.8=重启后生效！

of.options.AF_LEVEL=各向异性过滤
of.options.AF_LEVEL.tooltip.1=各向异性过滤
of.options.AF_LEVEL.tooltip.2= 关闭 - （默认）标准材质细节（较快）
of.options.AF_LEVEL.tooltip.3= 2-16 - 还原多级纹理(mipmap)处理后的的细节（较慢）
of.options.AF_LEVEL.tooltip.4=各向异性过滤还原了经多级
of.options.AF_LEVEL.tooltip.5=纹理过滤后的材质细节。
of.options.AF_LEVEL.tooltip.6=启用它可能会大幅降低 FPS。

of.options.CLEAR_WATER=清澈水体
of.options.CLEAR_WATER.tooltip.1=清澈水体
of.options.CLEAR_WATER.tooltip.2=  开启 - 清澈、透明水体
of.options.CLEAR_WATER.tooltip.3=  关闭 - 默认水体

of.options.RANDOM_ENTITIES=随机实体材质
of.options.RANDOM_ENTITIES.tooltip.1=随机实体材质
of.options.RANDOM_ENTITIES.tooltip.2=  关闭 - 关闭随机实体材质（较快）
of.options.RANDOM_ENTITIES.tooltip.3=  开启 - 随机实体材质（较慢）
of.options.RANDOM_ENTITIES.tooltip.4=游戏中的实体使用随机的相应贴图。
of.options.RANDOM_ENTITIES.tooltip.5=需要资源包内有多个实体贴图。

of.options.BETTER_GRASS=更好的草地
of.options.BETTER_GRASS.tooltip.1=更好的草地
of.options.BETTER_GRASS.tooltip.2=  关闭 - 默认草地材质（最快）
of.options.BETTER_GRASS.tooltip.3=  流畅 - 草方块侧面全部使用草地材质（较慢）
of.options.BETTER_GRASS.tooltip.4=  高品质 - 草方块侧面材质动态化（最慢）

of.options.BETTER_SNOW=更好的雪地
of.options.BETTER_SNOW.tooltip.1=更好的雪地
of.options.BETTER_SNOW.tooltip.2=  关闭 - 默认的雪地（较快）
of.options.BETTER_SNOW.tooltip.3=  开启 - 更好的雪地（较慢）
of.options.BETTER_SNOW.tooltip.4=在与雪接壤的“透明方块”
of.options.BETTER_SNOW.tooltip.5=（如栅栏、高草）下显示雪。

of.options.CUSTOM_FONTS=自定义字体
of.options.CUSTOM_FONTS.tooltip.1=自定义字体
of.options.CUSTOM_FONTS.tooltip.2=  开启 - （默认）使用自定义字体（较慢）
of.options.CUSTOM_FONTS.tooltip.3=  关闭 - 使用默认字体（较快）
of.options.CUSTOM_FONTS.tooltip.4=自定义字体一般由
of.options.CUSTOM_FONTS.tooltip.5=当前的资源包提供。

of.options.CUSTOM_COLORS=自定义色彩
of.options.CUSTOM_COLORS.tooltip.1=自定义色彩
of.options.CUSTOM_COLORS.tooltip.2=  开启 - （默认）使用自定义色彩（较慢）
of.options.CUSTOM_COLORS.tooltip.3=  关闭 - 使用默认色彩（较快）
of.options.CUSTOM_COLORS.tooltip.4=自定义色彩一般由
of.options.CUSTOM_COLORS.tooltip.5=当前的资源包提供。

of.options.SWAMP_COLORS=沼泽颜色
of.options.SWAMP_COLORS.tooltip.1=沼泽颜色
of.options.SWAMP_COLORS.tooltip.2=  开启 - （默认）使用沼泽颜色（较慢）
of.options.SWAMP_COLORS.tooltip.3=  关闭 - 不使用沼泽颜色（较快）
of.options.SWAMP_COLORS.tooltip.4=沼泽的颜色会影响草、树叶、藤蔓和水。

of.options.SMOOTH_BIOMES=平滑生物群系
of.options.SMOOTH_BIOMES.tooltip.1=平滑生物群系
of.options.SMOOTH_BIOMES.tooltip.2=  开启 - （默认）平滑生物群系的边界（较慢）
of.options.SMOOTH_BIOMES.tooltip.3=  关闭 - 不平滑生物群系的边界（较快）
of.options.SMOOTH_BIOMES.tooltip.4=取样边界附近所有方块颜色的平均值
of.options.SMOOTH_BIOMES.tooltip.5=以平滑生物群系的边界。
of.options.SMOOTH_BIOMES.tooltip.6=草、树叶、藤蔓和水会被受到影响。

of.options.CONNECTED_TEXTURES=连接纹理
of.options.CONNECTED_TEXTURES.tooltip.1=连接纹理
of.options.CONNECTED_TEXTURES.tooltip.2=  关闭 - 关闭连接纹理（默认）
of.options.CONNECTED_TEXTURES.tooltip.3=  流畅 - 快速处理纹理连接
of.options.CONNECTED_TEXTURES.tooltip.4=  高品质 - 精细处理纹理连接
of.options.CONNECTED_TEXTURES.tooltip.5=连接纹理为玻璃、沙石和书架增加了连接材质，
of.options.CONNECTED_TEXTURES.tooltip.6=当这些同类方块放在一起时会将材质连接为一体。
of.options.CONNECTED_TEXTURES.tooltip.7=连接材质一般由
of.options.CONNECTED_TEXTURES.tooltip.8=当前的资源包提供。

of.options.NATURAL_TEXTURES=自然纹理
of.options.NATURAL_TEXTURES.tooltip.1=自然纹理
of.options.NATURAL_TEXTURES.tooltip.2=  关闭 - 关闭自然纹理（默认）
of.options.NATURAL_TEXTURES.tooltip.3=  开启 - 启用自然纹理
of.options.NATURAL_TEXTURES.tooltip.4=自然纹理会移除由同一类型的方块重复铺设
of.options.NATURAL_TEXTURES.tooltip.5=而造成的栅格状图案。此功能通过旋转和翻转
of.options.NATURAL_TEXTURES.tooltip.6=方块的基础材质来创建材质变体。
of.options.NATURAL_TEXTURES.tooltip.7=自然纹理的配置一般由
of.options.NATURAL_TEXTURES.tooltip.8=当前的资源包提供。

of.options.EMISSIVE_TEXTURES=自发光纹理
of.options.EMISSIVE_TEXTURES.tooltip.1=自发光纹理
of.options.EMISSIVE_TEXTURES.tooltip.2=  关闭 - 关闭自发光纹理（默认）
of.options.EMISSIVE_TEXTURES.tooltip.3=  开启 - 启用自发光纹理
of.options.EMISSIVE_TEXTURES.tooltip.4=自发光纹理会作为覆盖层
of.options.EMISSIVE_TEXTURES.tooltip.5=以最大亮度渲染。可用于
of.options.EMISSIVE_TEXTURES.tooltip.6=模拟基础材质的发光部分。
of.options.EMISSIVE_TEXTURES.tooltip.7=自发光纹理一般由
of.options.EMISSIVE_TEXTURES.tooltip.8=当前的资源包提供。

of.options.CUSTOM_SKY=自定义天空
of.options.CUSTOM_SKY.tooltip.1=自定义天空
of.options.CUSTOM_SKY.tooltip.2=  开启 - （默认）自定义天空材质（较慢）
of.options.CUSTOM_SKY.tooltip.3=  关闭 - 默认天空材质（较快）
of.options.CUSTOM_SKY.tooltip.4=自定义天空的材质一般由
of.options.CUSTOM_SKY.tooltip.5=当前的资源包提供。

of.options.CUSTOM_ITEMS=自定义物品
of.options.CUSTOM_ITEMS.tooltip.1=自定义物品
of.options.CUSTOM_ITEMS.tooltip.2=  开启 - （默认）自定义物品材质（较慢）
of.options.CUSTOM_ITEMS.tooltip.3=  关闭 - 默认物品材质（较快）
of.options.CUSTOM_ITEMS.tooltip.4=自定义物品的材质一般由
of.options.CUSTOM_ITEMS.tooltip.5=当前的资源包提供。

of.options.CUSTOM_ENTITY_MODELS=自定义实体模型
of.options.CUSTOM_ENTITY_MODELS.tooltip.1=自定义实体模型
of.options.CUSTOM_ENTITY_MODELS.tooltip.2=  开启 - （默认）自定义实体模型（较慢）
of.options.CUSTOM_ENTITY_MODELS.tooltip.3=  关闭 - 默认实体模型（较快）
of.options.CUSTOM_ENTITY_MODELS.tooltip.4=自定义实体模型一般由
of.options.CUSTOM_ENTITY_MODELS.tooltip.5=当前的资源包提供。

of.options.CUSTOM_GUIS=自定义界面
of.options.CUSTOM_GUIS.tooltip.1=自定义界面
of.options.CUSTOM_GUIS.tooltip.2=  开启 - （默认）自定义图形用户界面（较慢）
of.options.CUSTOM_GUIS.tooltip.3=  关闭 - 默认图形用户界面（较快）
of.options.CUSTOM_GUIS.tooltip.4=自定义图形用户界面一般由当前的资源包提供。

# Details

of.options.CLOUDS=云
of.options.CLOUDS.tooltip.1=云
of.options.CLOUDS.tooltip.2=  默认 - 以“图形品质”的设定为准
of.options.CLOUDS.tooltip.3=  流畅 - 低品质（较快）
of.options.CLOUDS.tooltip.4=  高品质 - 高品质（较慢）
of.options.CLOUDS.tooltip.5=  关闭 - 禁用云（最快）
of.options.CLOUDS.tooltip.6=低品质云使用 2D 渲染，
of.options.CLOUDS.tooltip.7=高品质云使用 3D 渲染。

of.options.CLOUD_HEIGHT=云高度
of.options.CLOUD_HEIGHT.tooltip.1=云高度
of.options.CLOUD_HEIGHT.tooltip.2=  关闭 - 默认高度
of.options.CLOUD_HEIGHT.tooltip.3=  100%% - 超过世界的高度限制

of.options.TREES=树
of.options.TREES.tooltip.1=树
of.options.TREES.tooltip.2=  默认 - 以“图形品质”的设定为准
of.options.TREES.tooltip.3=  流畅 - 低品质（最快）
of.options.TREES.tooltip.4=  智能 - 较高品质（较快）
of.options.TREES.tooltip.5=  高品质 - 高品质（较慢）
of.options.TREES.tooltip.6=低品质的树叶不透明，
of.options.TREES.tooltip.7=高品质的树叶透明、镂空。

of.options.RAIN=雨雪
of.options.RAIN.tooltip.1=雨雪
of.options.RAIN.tooltip.2=  默认 - 以“图形品质”的设定为准
of.options.RAIN.tooltip.3=  流畅  - 少量的雨/雪（较快）
of.options.RAIN.tooltip.4=  高品质 - 大量的雨/雪（较慢）
of.options.RAIN.tooltip.5=  关闭 - 禁用雨/雪（最快）
of.options.RAIN.tooltip.6=雨声 和 雨滴飞溅
of.options.RAIN.tooltip.7=不受此选项影响。

of.options.SKY=天空
of.options.SKY.tooltip.1=天空
of.options.SKY.tooltip.2=  开启 - 天空可见（较慢）
of.options.SKY.tooltip.3=  关闭  - 天空不可见（较快）
of.options.SKY.tooltip.4=日月的可见与否有单独的选项。

of.options.STARS=星星
of.options.STARS.tooltip.1=星星
of.options.STARS.tooltip.2=  开启 - 星星可见（较慢）
of.options.STARS.tooltip.3=  关闭  - 星星不可见（较快）

of.options.SUN_MOON=日月
of.options.SUN_MOON.tooltip.1=日月
of.options.SUN_MOON.tooltip.2=  开启 - 太阳和月亮可见（默认）
of.options.SUN_MOON.tooltip.3=  关闭  - 太阳和月亮不可见（较快）

of.options.SHOW_CAPES=显示披风
of.options.SHOW_CAPES.tooltip.1=显示披风
of.options.SHOW_CAPES.tooltip.2=  开启 - 显示玩家披风（默认）
of.options.SHOW_CAPES.tooltip.3=  关闭 - 不显示玩家披风

of.options.TRANSLUCENT_BLOCKS=半透明方块
of.options.TRANSLUCENT_BLOCKS.tooltip.1=半透明方块
of.options.TRANSLUCENT_BLOCKS.tooltip.2=   默认 - 以“图形品质”的设定为准
of.options.TRANSLUCENT_BLOCKS.tooltip.3=  高品质 - 准确的颜色混合（默认）
of.options.TRANSLUCENT_BLOCKS.tooltip.4=  流畅 - 快速的颜色混合（较快）
of.options.TRANSLUCENT_BLOCKS.tooltip.5=控制不同颜色的半透明方块
of.options.TRANSLUCENT_BLOCKS.tooltip.6=（如染色玻璃、水、冰）间
of.options.TRANSLUCENT_BLOCKS.tooltip.7=有空气存在时，彼此颜色的混合。

of.options.HELD_ITEM_TOOLTIPS=持有物信息显示
of.options.HELD_ITEM_TOOLTIPS.tooltip.1=持有物信息显示
of.options.HELD_ITEM_TOOLTIPS.tooltip.2=  开启 - 显示持有物信息（默认）
of.options.HELD_ITEM_TOOLTIPS.tooltip.3=  关闭 - 隐藏持有物信息

of.options.ADVANCED_TOOLTIPS=高级信息提示
of.options.ADVANCED_TOOLTIPS.tooltip.1=高级信息提示
of.options.ADVANCED_TOOLTIPS.tooltip.2=  开启 - 显示高级信息提示
of.options.ADVANCED_TOOLTIPS.tooltip.3=  关闭 - 隐藏高级信息提示（默认）
of.options.ADVANCED_TOOLTIPS.tooltip.4=物品（ID、耐久度）和光影设置
of.options.ADVANCED_TOOLTIPS.tooltip.5=（ID、源、默认值）的额外信息
of.options.ADVANCED_TOOLTIPS.tooltip.6=会在开启时显示。

of.options.DROPPED_ITEMS=掉落物
of.options.DROPPED_ITEMS.tooltip.1=掉落物
of.options.DROPPED_ITEMS.tooltip.2=  默认 - 以“图形品质”的设定为准
of.options.DROPPED_ITEMS.tooltip.3=  流畅 - 2D 掉落物（较快）
of.options.DROPPED_ITEMS.tooltip.4=  高品质 - 3D 掉落物（较慢）

options.entityShadows.tooltip.1=实体阴影
options.entityShadows.tooltip.2=  开启 - 显示实体阴影
options.entityShadows.tooltip.3=  关闭 - 隐藏实体阴影

of.options.VIGNETTE=晕影
of.options.VIGNETTE.tooltip.1=使屏幕四角轻微变暗的视觉效果
of.options.VIGNETTE.tooltip.2=  默认 - 以“图形品质”的设定为准（默认）
of.options.VIGNETTE.tooltip.3=  流畅 - 晕影关闭（较快）
of.options.VIGNETTE.tooltip.4=  高品质 - 晕影开启（较慢）
of.options.VIGNETTE.tooltip.5=晕影可能对FPS有显著的影响，
of.options.VIGNETTE.tooltip.6=尤其是全屏游戏的时候。
of.options.VIGNETTE.tooltip.7=晕影的效果非常细微，
of.options.VIGNETTE.tooltip.8=并且可以被安全地禁用。

of.options.DYNAMIC_FOV=动态视场
of.options.DYNAMIC_FOV.tooltip.1=动态视场
of.options.DYNAMIC_FOV.tooltip.2=  开启 - 启用动态视场（默认）
of.options.DYNAMIC_FOV.tooltip.3=  关闭 - 禁用动态视场
of.options.DYNAMIC_FOV.tooltip.4=当飞行、疾跑或 
of.options.DYNAMIC_FOV.tooltip.5=拉弓时改变视场。

of.options.DYNAMIC_LIGHTS=动态光源
of.options.DYNAMIC_LIGHTS.tooltip.1=动态光源
of.options.DYNAMIC_LIGHTS.tooltip.2=  关闭 - 无动态光源（默认）
of.options.DYNAMIC_LIGHTS.tooltip.3=  流畅 - 较快的动态光源（每 500ms 更新一次）
of.options.DYNAMIC_LIGHTS.tooltip.4=  高品质 - 高品质的动态光源（实时更新）
of.options.DYNAMIC_LIGHTS.tooltip.5=使得发光的物品（火把、荧石等）
of.options.DYNAMIC_LIGHTS.tooltip.6=被玩家左右手持握、装备或
of.options.DYNAMIC_LIGHTS.tooltip.7=成为掉落物时照亮周围。

options.biomeBlendRadius.tooltip.1=平滑生物群系之间的颜色过渡
options.biomeBlendRadius.tooltip.2=  关 - 无混色（最快）
options.biomeBlendRadius.tooltip.3=  5x5 - 正常混色（默认）
options.biomeBlendRadius.tooltip.4=  15x15 - 最大混色（最慢）
options.biomeBlendRadius.tooltip.5=较大的值可能会产生显著的突发延迟
options.biomeBlendRadius.tooltip.6=并减慢加载区块的速度。

# Performance

of.options.SMOOTH_FPS=平滑 FPS
of.options.SMOOTH_FPS.tooltip.1=通过清除显卡缓冲区来稳定 FPS
of.options.SMOOTH_FPS.tooltip.2=  关闭 - 不稳定，FPS 可能波动
of.options.SMOOTH_FPS.tooltip.3=  开启 - FPS 稳定
of.options.SMOOTH_FPS.tooltip.4=此选项依赖于显卡驱动，
of.options.SMOOTH_FPS.tooltip.5=通常效果并不明显。

of.options.SMOOTH_WORLD=平滑世界
of.options.SMOOTH_WORLD.tooltip.1=消除内置服务器造成的突发延迟。
of.options.SMOOTH_WORLD.tooltip.2=  关闭 - 不稳定，FPS 可能波动
of.options.SMOOTH_WORLD.tooltip.3=  开启 - FPS 稳定
of.options.SMOOTH_WORLD.tooltip.4=分担内置服务器负载来稳定FPS。
of.options.SMOOTH_WORLD.tooltip.5=只在本地世界（单人游戏）有效。

of.options.FAST_RENDER=快速渲染
of.options.FAST_RENDER.tooltip.1=快速渲染
of.options.FAST_RENDER.tooltip.2= 关闭 - 标准渲染（默认）
of.options.FAST_RENDER.tooltip.3= 开启 - 优化渲染（较快）
of.options.FAST_RENDER.tooltip.4=采用优化渲染算法从而降低 CPU 的负载
of.options.FAST_RENDER.tooltip.5=并且可能大幅提升 FPS。
of.options.FAST_RENDER.tooltip.6=此选项或与某些模组冲突。

of.options.FAST_MATH=快速运算
of.options.FAST_MATH.tooltip.1=快速运算
of.options.FAST_MATH.tooltip.2= 关闭 - 标准的运算（默认）
of.options.FAST_MATH.tooltip.3= 开启 - 更快的运算
of.options.FAST_MATH.tooltip.4=采用优化的 sin() 和 cos() 函数可以
of.options.FAST_MATH.tooltip.5=更好地利用 CPU 缓存，并且提升 FPS。
of.options.FAST_MATH.tooltip.6=此选项对世界生成有微小影响。

of.options.CHUNK_UPDATES=区块更新
of.options.CHUNK_UPDATES.tooltip.1=区块更新
of.options.CHUNK_UPDATES.tooltip.2= 1 - 世界载入速度较慢，FPS 较高（默认）
of.options.CHUNK_UPDATES.tooltip.3= 3 - 世界载入速度较快，FPS 较低
of.options.CHUNK_UPDATES.tooltip.4= 5 - 世界载入速度最快，FPS 最低
of.options.CHUNK_UPDATES.tooltip.5=渲染每帧时更新的区块数，
of.options.CHUNK_UPDATES.tooltip.6=更高的值将会导致帧数不稳定。

of.options.CHUNK_UPDATES_DYNAMIC=动态更新
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.1=动态区块更新
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.2= 关闭 - 标准的每帧区块更新（默认）
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.3= 开启 - 当玩家站立不动时更多的区块更新
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.4=当玩家站立不动时，强迫更多的
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.5=区块更新，使得世界加载更快。

of.options.LAZY_CHUNK_LOADING=缓慢区块加载
of.options.LAZY_CHUNK_LOADING.tooltip.1=缓慢区块加载
of.options.LAZY_CHUNK_LOADING.tooltip.2= 关闭 - 默认的服务器区块载入
of.options.LAZY_CHUNK_LOADING.tooltip.3= 开启 - 缓慢的服务器区块载入（更平滑）
of.options.LAZY_CHUNK_LOADING.tooltip.4=将区块分布在多个“游戏刻”进行加载
of.options.LAZY_CHUNK_LOADING.tooltip.5=从而平滑服务器整体区块加载。
of.options.LAZY_CHUNK_LOADING.tooltip.6=如果部分世界不能正确地载入，请把它设置为关。
of.options.LAZY_CHUNK_LOADING.tooltip.7=仅适用于单人游戏的本地世界。

of.options.RENDER_REGIONS=区域渲染
of.options.RENDER_REGIONS.tooltip.1=区域渲染
of.options.RENDER_REGIONS.tooltip.2= 关闭 - 原版的渲染（默认）
of.options.RENDER_REGIONS.tooltip.3= 开启 - 使用区域渲染（更快）
of.options.RENDER_REGIONS.tooltip.4=通过优化 GPU 负载加速地形渲染。
of.options.RENDER_REGIONS.tooltip.5=渲染距离较高时此选项将更有效。
of.options.RENDER_REGIONS.tooltip.6=不推荐集成显卡使用。

of.options.SMART_ANIMATIONS=智能动态材质
of.options.SMART_ANIMATIONS.tooltip.1=智能动态材质
of.options.SMART_ANIMATIONS.tooltip.2= 关闭 - 不使用智能动态材质（默认）
of.options.SMART_ANIMATIONS.tooltip.3= 开启 - 使用智能动态材质（更快）
of.options.SMART_ANIMATIONS.tooltip.4=当使用智能动态材质时，游戏仅更新
of.options.SMART_ANIMATIONS.tooltip.5=当前屏幕可见的动态材质。
of.options.SMART_ANIMATIONS.tooltip.6=此选项可减少游戏刻突发延迟并提升 FPS。
of.options.SMART_ANIMATIONS.tooltip.7=对于大型模组包和高清资源包格外有效。

# Animations

of.options.animation.allOn=全部开启
of.options.animation.allOff=全部关闭
of.options.animation.dynamic=动态

of.options.ANIMATED_WATER=水面动画
of.options.ANIMATED_LAVA=熔岩动画
of.options.ANIMATED_FIRE=火焰动画
of.options.ANIMATED_PORTAL=传送门动画
of.options.ANIMATED_REDSTONE=红石动画
of.options.ANIMATED_EXPLOSION=爆炸动画
of.options.ANIMATED_FLAME=火焰动画
of.options.ANIMATED_SMOKE=烟雾动画
of.options.VOID_PARTICLES=虚空粒子
of.options.WATER_PARTICLES=水面粒子
of.options.RAIN_SPLASH=雨滴飞溅
of.options.PORTAL_PARTICLES=传送门粒子
of.options.POTION_PARTICLES=药水粒子
of.options.DRIPPING_WATER_LAVA=水滴/熔岩滴
of.options.ANIMATED_TERRAIN=地形动画
of.options.ANIMATED_TEXTURES=动态材质
of.options.FIREWORK_PARTICLES=烟花粒子

# Other

of.options.LAGOMETER=快速调试信息
of.options.LAGOMETER.tooltip.1=在调试界面（F3）显示快速调试信息。
of.options.LAGOMETER.tooltip.2=* 橙 - 内存垃圾回收
of.options.LAGOMETER.tooltip.3=* 青 - 游戏刻
of.options.LAGOMETER.tooltip.4=* 蓝 - 预定可执行
of.options.LAGOMETER.tooltip.5=* 紫 - 区块加载
of.options.LAGOMETER.tooltip.6=* 红 - 区块更新
of.options.LAGOMETER.tooltip.7=* 黄 - 能见度检查
of.options.LAGOMETER.tooltip.8=* 绿 - 渲染地形

of.options.PROFILER=调试分析器
of.options.PROFILER.tooltip.1=调试分析器
of.options.PROFILER.tooltip.2=  开启 - 调试分析器启用（较慢）
of.options.PROFILER.tooltip.3=  关闭 - 调试分析器禁用（较快）
of.options.PROFILER.tooltip.4=在调试界面开启状态（F3）时
of.options.PROFILER.tooltip.5=调试分析器收集并且显示调试信息。

of.options.WEATHER=天气
of.options.WEATHER.tooltip.1=天气
of.options.WEATHER.tooltip.2=  开启 - 开启天气（较慢）
of.options.WEATHER.tooltip.3=  关闭 - 关闭天气（较快）
of.options.WEATHER.tooltip.4=天气选项影响雨，雪和雷电。
of.options.WEATHER.tooltip.5=天气选项仅在本地游戏中生效。

of.options.time.dayOnly=只有白天
of.options.time.nightOnly=只有夜晚

of.options.TIME=时间
of.options.TIME.tooltip.1=时间
of.options.TIME.tooltip.2= 默认 - 正常的日夜交替
of.options.TIME.tooltip.3= 只有白天 - 只有白天
of.options.TIME.tooltip.4= 只有夜晚 - 只有夜晚
of.options.TIME.tooltip.5=时间设置只在创造模式下
of.options.TIME.tooltip.6=且为本地游戏时有效。

options.fullscreen.tooltip.1=全屏
options.fullscreen.tooltip.2=  开启 - 使用全屏模式
options.fullscreen.tooltip.3=  关闭 - 使用窗口模式
options.fullscreen.tooltip.4=全屏模式可能比窗口模式
options.fullscreen.tooltip.5=更快或更慢，这取决于显卡。

of.options.FULLSCREEN_MODE=全屏模式
of.options.FULLSCREEN_MODE.tooltip.1=全屏模式
of.options.FULLSCREEN_MODE.tooltip.2=  默认 - 使用桌面分辨率（较慢）
of.options.FULLSCREEN_MODE.tooltip.3=  自定义 - 使用自定义屏幕分辨率，可能会较快
of.options.FULLSCREEN_MODE.tooltip.4=这个选项只有在全屏模式下生效（F11）。
of.options.FULLSCREEN_MODE.tooltip.5=较低的分辨率通常会更快。

of.options.SHOW_FPS=显示 FPS
of.options.SHOW_FPS.tooltip.1=显示迷你的 FPS 和渲染信息
of.options.SHOW_FPS.tooltip.2=  FPS - 平均/最低
of.options.SHOW_FPS.tooltip.3=  C：- 区块渲染器
of.options.SHOW_FPS.tooltip.4=  E：- 一般实体 + 方块实体
of.options.SHOW_FPS.tooltip.5=  U：- 区块更新
of.options.SHOW_FPS.tooltip.6=只有调试屏幕隐藏时
of.options.SHOW_FPS.tooltip.7=迷你FPS信息才会显示。

of.options.save.45s=45 秒
of.options.save.90s=90 秒
of.options.save.3min=3 分钟
of.options.save.6min=6 分钟
of.options.save.12min=12 分钟
of.options.save.24min=24 分钟

of.options.AUTOSAVE_TICKS=自动保存
of.options.AUTOSAVE_TICKS.tooltip.1=自动保存间隔
of.options.AUTOSAVE_TICKS.tooltip.2= 45 秒 - 默认
of.options.AUTOSAVE_TICKS.tooltip.3=自动保存或许会导致突发延迟，这取决于渲染距离。
of.options.AUTOSAVE_TICKS.tooltip.4=当打开游戏菜单时，世界也会保存。

of.options.SCREENSHOT_SIZE=截图尺寸
of.options.SCREENSHOT_SIZE.tooltip.1=截图尺寸
of.options.SCREENSHOT_SIZE.tooltip.2=  默认 - 默认的截图尺寸
of.options.SCREENSHOT_SIZE.tooltip.3=  2x-4x - 自定义截图尺寸
of.options.SCREENSHOT_SIZE.tooltip.4=抓取更大的截图可能需要更多的内存。
of.options.SCREENSHOT_SIZE.tooltip.5=与快速渲染和抗锯齿不兼容。
of.options.SCREENSHOT_SIZE.tooltip.6=需要显卡帧缓冲区支持。

of.options.SHOW_GL_ERRORS=显示 GL 错误
of.options.SHOW_GL_ERRORS.tooltip.1=显示 OpenGL 错误
of.options.SHOW_GL_ERRORS.tooltip.2=当启用此选项时，OpenGL 错误会在聊天框中显示出来。
of.options.SHOW_GL_ERRORS.tooltip.3=请仅当所有冲突都已知并且
of.options.SHOW_GL_ERRORS.tooltip.4=明确知道错误无法修复时禁用此选项。
of.options.SHOW_GL_ERRORS.tooltip.5=即使禁用了此选项，错误仍会被记录在错误日志中，
of.options.SHOW_GL_ERRORS.tooltip.6=并且这些错误仍可能会导致明显的 FPS 下降。

# Chat Settings

of.options.CHAT_BACKGROUND=聊天背景
of.options.CHAT_BACKGROUND.tooltip.1=聊天背景
of.options.CHAT_BACKGROUND.tooltip.2=  默认 - 固定字宽
of.options.CHAT_BACKGROUND.tooltip.3=  紧凑 - 调整以适应行宽
of.options.CHAT_BACKGROUND.tooltip.4=  关闭 - 隐藏

of.options.CHAT_SHADOW=聊天阴影
of.options.CHAT_SHADOW.tooltip.1=聊天阴影
of.options.CHAT_SHADOW.tooltip.2=  开启 - 使用字体阴影
of.options.CHAT_SHADOW.tooltip.3=  关闭 - 无字体阴影
